import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder, In } from 'typeorm';
import { File } from '@modules/business/entities';
import { PaginatedResult } from '@common/response';
import { SortDirection } from '@common/dto/query.dto';

/**
 * Repository xử lý truy vấn dữ liệu cho entity File
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến file
 */
@Injectable()
export class FileRepository extends Repository<File> {
  private readonly logger = new Logger(FileRepository.name);

  constructor(private readonly dataSource: DataSource) {
    super(File, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho File (Admin)
   * @returns Query builder
   */
  private createBaseQueryAdmin(): SelectQueryBuilder<File> {
    return this.createQueryBuilder('file');
  }

  /**
   * Tạo query builder cơ bản cho File (User)
   * @returns SelectQueryBuilder cho File
   */
  private createBaseQueryUser(): SelectQueryBuilder<File> {
    return this.createQueryBuilder('file')
      .select([
        'file.id',
        'file.folderId',
        'file.name',
        'file.storageKey',
        'file.size',
        'file.createdAt',
        'file.updatedAt'
      ]);
  }

  /**
   * Tạo file mới
   * @param file Thông tin file
   * @returns File đã tạo
   */
  async createFile(file: File): Promise<File> {
    try {
      return await this.save(file);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo file: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo file: ${error.message}`);
    }
  }

  /**
   * Tạo nhiều file cùng lúc
   * @param files Danh sách thông tin file
   * @returns Danh sách file đã tạo
   */
  async createMultipleFiles(files: File[]): Promise<File[]> {
    try {
      return await this.save(files);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo nhiều file: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tạo nhiều file: ${error.message}`);
    }
  }

  /**
   * Tìm file theo ID (Admin)
   * @param id ID của file
   * @returns Thông tin file hoặc null nếu không tìm thấy
   */
  async findByIdAdmin(id: number): Promise<File | null> {
    try {
      return await this.createBaseQueryAdmin()
        .where('file.id = :id', { id })
        .getOne();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm file theo ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi tìm file theo ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm file theo ID (User)
   * @param id ID của file
   * @returns File nếu tìm thấy, null nếu không tìm thấy
   */
  async findByIdUser(id: number): Promise<File | null> {
    this.logger.log(`Tìm file với ID: ${id}`);

    return this.createBaseQueryUser()
      .where('file.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm file theo ID thư mục
   * @param folderId ID của thư mục
   * @returns Danh sách file trong thư mục
   */
  async findByFolderId(folderId: number): Promise<File[]> {
    try {
      return await this.createBaseQueryAdmin()
        .where('file.folderId = :folderId', { folderId })
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm file theo ID thư mục ${folderId}: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi tìm file theo ID thư mục ${folderId}: ${error.message}`,
      );
    }
  }

  /**
   * Tìm nhiều file theo danh sách ID
   * @param ids Danh sách ID của các file
   * @returns Danh sách file tìm thấy
   */
  async findByIds(ids: number[]): Promise<File[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    try {
      return await this.createBaseQueryAdmin()
        .where('file.id IN (:...ids)', { ids })
        .getMany();
    } catch (error) {
      this.logger.error(
        `Lỗi khi tìm file theo danh sách ID: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Lỗi khi tìm file theo danh sách ID: ${error.message}`,
      );
    }
  }

  /**
   * Tìm file theo ID kho ảo
   * @param warehouseId ID của kho ảo
   * @returns Danh sách file trong kho ảo
   * @deprecated Không sử dụng vì không có cột warehouse_id trong database
   */
  async findByWarehouseId(warehouseId: number): Promise<File[]> {
    this.logger.warn(`Phương thức findByWarehouseId không hoạt động vì không có cột warehouse_id trong database`);
    return [];
  }

  /**
   * Cập nhật file
   * @param id ID của file
   * @param updateData Dữ liệu cập nhật
   * @returns File đã cập nhật
   */
  async updateFile(id: number, updateData: Partial<File>): Promise<File> {
    try {
      await this.update({ id }, updateData);
      const updatedFile = await this.findByIdAdmin(id);
      if (!updatedFile) {
        throw new Error(`Không tìm thấy file với ID ${id} sau khi cập nhật`);
      }
      return updatedFile;
    } catch (error) {
      this.logger.error(
        `Lỗi khi cập nhật file với ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi cập nhật file với ID ${id}: ${error.message}`);
    }
  }

  /**
   * Xóa file
   * @param id ID của file
   * @returns Kết quả xóa
   */
  async deleteFile(id: number): Promise<boolean> {
    try {
      const result = await this.delete({ id });
      return (
        result.affected !== undefined &&
        result.affected !== null &&
        result.affected > 0
      );
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa file với ID ${id}: ${error.message}`,
        error.stack,
      );
      throw new Error(`Lỗi khi xóa file với ID ${id}: ${error.message}`);
    }
  }

  /**
   * Tìm kiếm file với các điều kiện lọc và phân trang (Admin)
   * @param queryParams Tham số truy vấn
   * @returns Danh sách file với phân trang
   */
  async findAllAdmin(queryParams: any): Promise<PaginatedResult<File>> {
    try {
      const {
        page = 1,
        limit = 10,
        offset = (page - 1) * limit,
        search,
        folderId,
        warehouseId,
        extension,
        sortBy = 'id',
        sortDirection = 'ASC',
      } = queryParams;

      // Tạo query builder
      const queryBuilder = this.createBaseQueryAdmin();

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        queryBuilder.andWhere('file.name ILIKE :search', {
          search: `%${search}%`,
        });
      }

      // Thêm điều kiện lọc theo thư mục nếu có
      if (folderId) {
        queryBuilder.andWhere('file.folderId = :folderId', { folderId });
      }

      // Không lọc theo warehouseId vì không có cột warehouse_id trong database
      if (warehouseId) {
        this.logger.warn(`Không thể lọc theo warehouseId vì không có cột warehouse_id trong database`);
      }

      // Thêm điều kiện lọc theo phần mở rộng nếu có
      if (extension) {
        queryBuilder.andWhere('file.extension = :extension', { extension });
      }

      // Đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Thêm sắp xếp và phân trang
      queryBuilder
        .orderBy(`file.${sortBy}`, sortDirection as 'ASC' | 'DESC')
        .skip(offset)
        .take(limit);

      // Lấy danh sách file
      const items = await queryBuilder.getMany();

      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tìm kiếm file: ${error.message}`, error.stack);
      throw new Error(`Lỗi khi tìm kiếm file: ${error.message}`);
    }
  }

  /**
   * Lấy danh sách file với phân trang và tìm kiếm (User)
   * @param page Số trang
   * @param limit Số lượng bản ghi trên mỗi trang
   * @param search Từ khóa tìm kiếm
   * @param folderId ID thư mục (nếu có)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @param userId ID người dùng (nếu có)
   * @returns Danh sách file với phân trang
   */
  async findAllUser(
    page: number = 1,
    limit: number = 10,
    search?: string,
    folderId?: number,
    sortBy: string = 'createdAt',
    sortDirection: SortDirection = SortDirection.DESC,
    userId?: number,
  ): Promise<PaginatedResult<File>> {
    this.logger.log(`Lấy danh sách file với page=${page}, limit=${limit}, search=${search}, folderId=${folderId}, userId=${userId}`);

    const queryBuilder = this.createBaseQueryUser();

    // Áp dụng điều kiện tìm kiếm
    if (search) {
      queryBuilder.andWhere('LOWER(file.name) LIKE LOWER(:search)', { search: `%${search}%` });
    }

    // Lọc theo thư mục
    if (folderId) {
      queryBuilder.andWhere('file.folderId = :folderId', { folderId });
    }

    // Lọc theo người dùng
    if (userId) {
      queryBuilder.innerJoin('folder', 'folder', 'file.folderId = folder.id')
        .andWhere('folder.userId = :userId', { userId });
    }

    // Áp dụng sắp xếp
    const direction = sortDirection === SortDirection.ASC ? 'ASC' : 'DESC';
    queryBuilder.orderBy(`file.${sortBy}`, direction);

    // Đếm tổng số bản ghi
    const totalItems = await queryBuilder.getCount();

    // Áp dụng phân trang
    queryBuilder.skip((page - 1) * limit).take(limit);

    // Thực hiện truy vấn
    const items = await queryBuilder.getMany();

    // Trả về kết quả phân trang
    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }
}