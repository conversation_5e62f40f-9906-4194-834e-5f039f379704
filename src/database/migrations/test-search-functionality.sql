-- Test script để kiểm tra tính năng tìm kiếm không dấu
-- Date: 2025-01-20
-- Description: Script test tìm kiếm khách hàng với từ khóa có dấu và không dấu

-- Tạo dữ liệu test (chỉ chạy nếu cần thiết)
-- INSERT INTO user_convert_customers (name, phone, platform, user_id, created_at, updated_at) VALUES
-- ('<PERSON>uyễn <PERSON>ăn <PERSON>', '0901234567', 'Facebook', 1, EXTRACT(epoch FROM now()) * 1000, EXTRACT(epoch FROM now()) * 1000),
-- ('Trần Thị Chiến Dịch', '0901234568', 'Web', 1, EXTRACT(epoch FROM now()) * 1000, EXTRACT(epoch FROM now()) * 1000),
-- ('<PERSON><PERSON> <PERSON>', '0901234569', 'Zalo', 1, EXTRACT(epoch FROM now()) * 1000, EXTRACT(epoch FROM now()) * 1000),
-- ('<PERSON>ạm Thị Hò<PERSON>', '0901234570', 'Facebook', 1, EXTRACT(epoch FROM now()) * 1000, EXTRACT(epoch FROM now()) * 1000);

-- Test queries để kiểm tra tìm kiếm
-- 1. Tìm kiếm với từ khóa có dấu "chiến"
SELECT name, phone, platform 
FROM user_convert_customers 
WHERE name ILIKE '%chiến%';

-- 2. Tìm kiếm với từ khóa không dấu "chien"
SELECT name, phone, platform 
FROM user_convert_customers 
WHERE LOWER(name) ILIKE '%chien%';

-- 3. Tìm kiếm với function loại bỏ dấu (sử dụng translate)
SELECT name, phone, platform 
FROM user_convert_customers 
WHERE LOWER(
  translate(
    name, 
    'àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđÀÁẠẢÃÂẦẤẬẨẪĂẰẮẶẲẴÈÉẸẺẼÊỀẾỆỂỄÌÍỊỈĨÒÓỌỎÕÔỒỐỘỔỖƠỜỚỢỞỠÙÚỤỦŨƯỪỨỰỬỮỲÝỴỶỸĐ',
    'aaaaaaaaaaaaaaaaaeeeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuuyyyyydAAAAAAAAAAAAAAAAAEEEEEEEEEEEEIIIIIOOOOOOOOOOOOOOOOOUUUUUUUUUUUYYYYYD'
  )
) ILIKE '%chien%';

-- 4. Test query tương tự như trong SearchHelper
SELECT name, phone, platform 
FROM user_convert_customers 
WHERE (
  name ILIKE '%chien%' OR 
  name ILIKE '%chiến%' OR
  LOWER(name) ILIKE '%chien%'
);

-- Kiểm tra performance với EXPLAIN
EXPLAIN ANALYZE 
SELECT name, phone, platform 
FROM user_convert_customers 
WHERE (
  name ILIKE '%chiến%' OR 
  name ILIKE '%chien%' OR
  LOWER(name) ILIKE '%chien%'
);

-- Tạo index để tối ưu tìm kiếm (nếu cần)
-- CREATE INDEX IF NOT EXISTS idx_user_convert_customers_name_gin 
-- ON user_convert_customers USING gin(name gin_trgm_ops);

-- CREATE INDEX IF NOT EXISTS idx_user_convert_customers_phone_gin 
-- ON user_convert_customers USING gin(phone gin_trgm_ops);
