import { Test, TestingModule } from '@nestjs/testing';
import { UserFileController } from '../../controllers/user-file.controller';
import { UserFileService } from '../../services/user-file.service';
import { CreateFileDto, UpdateFileDto, QueryFileDto, CreateFilesMultipleDto, CreateFilesMultipleResponseDto } from '../../dto/file';
import { FileResponseDto } from '../../dto/file/file-response.dto';
import { AppException } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { JwtPayload } from '@common/interfaces';
import { PaginatedResult } from '@common/response';

describe('UserFileController', () => {
  let controller: UserFileController;
  let service: UserFileService;

  // Mock data
  const mockUser: JwtPayload = {
    id: 1,
    email: '<EMAIL>',
    role: 'user',
  };

  const mockFileResponse: FileResponseDto = {
    id: 1,
    name: 'File 1.pdf',
    folderId: 1,
    warehouseId: 1,
    storageKey: 'files/2023/05/file1.pdf',
    size: 1024,
    viewUrl: 'https://cdn.example.com/files/2023/05/file1.pdf',
    uploadUrl: 'https://s3.example.com/upload-url',
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
  };

  const mockFileResponseList: FileResponseDto[] = [
    mockFileResponse,
    {
      id: 2,
      name: 'File 2.docx',
      folderId: 1,
      warehouseId: 1,
      storageKey: 'files/2023/05/file2.docx',
      size: 2048,
      viewUrl: 'https://cdn.example.com/files/2023/05/file2.docx',
      createdAt: 1625097700000,
      updatedAt: 1625097700000,
    },
  ];

  const mockPaginatedResult: PaginatedResult<FileResponseDto> = {
    items: mockFileResponseList,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UserFileController],
      providers: [
        {
          provide: UserFileService,
          useValue: {
            createFile: jest.fn(),
            createMultipleFiles: jest.fn(),
            updateFile: jest.fn(),
            getFileById: jest.fn(),
            getFiles: jest.fn(),
            getFilesByFolderId: jest.fn(),
            deleteFile: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<UserFileController>(UserFileController);
    service = module.get<UserFileService>(UserFileService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('createFile', () => {
    it('nên tạo file mới thành công', async () => {
      // Arrange
      const createDto: CreateFileDto = {
        name: 'New File.pdf',
        folderId: 1,
        size: 1024,
        warehouseId: 1,
      };

      jest.spyOn(service, 'createFile').mockResolvedValue(mockFileResponse);

      // Act
      const result = await controller.createFile(createDto);

      // Assert
      expect(service.createFile).toHaveBeenCalledWith(createDto, createDto.warehouseId);
      expect(result.data).toEqual(mockFileResponse);
      expect(result.message).toBe('Tạo file thành công');
    });

    it('nên ném lỗi khi tạo file thất bại', async () => {
      // Arrange
      const createDto: CreateFileDto = {
        name: 'New File.pdf',
        folderId: 1,
        size: 1024,
        warehouseId: 1,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_CREATION_FAILED, 'Lỗi khi tạo file');

      jest.spyOn(service, 'createFile').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.createFile(createDto)).rejects.toThrow(AppException);
      expect(service.createFile).toHaveBeenCalledWith(createDto, createDto.warehouseId);
    });
  });

  describe('createMultipleFiles', () => {
    it('nên tạo nhiều file thành công', async () => {
      // Arrange
      const createDto: CreateFilesMultipleDto = {
        folderId: 1,
        warehouseId: 1,
        files: [
          { name: 'File 1.pdf', size: 1024 },
          { name: 'File 2.docx', size: 2048 },
        ],
      };

      const mockResponse: CreateFilesMultipleResponseDto = {
        files: [
          {
            id: 1,
            name: 'File 1.pdf',
            uploadUrl: 'https://s3.example.com/upload-url-1',
            storageKey: 'files/2023/05/file1.pdf',
            viewUrl: 'https://cdn.example.com/files/2023/05/file1.pdf',
            warehouseId: 1,
            folderId: 1,
            size: 1024,
            createdAt: 1625097600000,
            updatedAt: 1625097600000,
          },
          {
            id: 2,
            name: 'File 2.docx',
            uploadUrl: 'https://s3.example.com/upload-url-2',
            storageKey: 'files/2023/05/file2.docx',
            viewUrl: 'https://cdn.example.com/files/2023/05/file2.docx',
            warehouseId: 1,
            folderId: 1,
            size: 2048,
            createdAt: 1625097700000,
            updatedAt: 1625097700000,
          },
        ],
        successCount: 2,
        totalCount: 2,
      };

      jest.spyOn(service, 'createMultipleFiles').mockResolvedValue(mockResponse);

      // Act
      const result = await controller.createMultipleFiles(createDto, mockUser);

      // Assert
      expect(createDto.userId).toBe(mockUser.id);
      expect(service.createMultipleFiles).toHaveBeenCalledWith(createDto);
      expect(result.result).toEqual(mockResponse);
      expect(result.message).toBe('Tạo 2 file thành công');
    });

    it('nên ném lỗi khi tạo nhiều file thất bại', async () => {
      // Arrange
      const createDto: CreateFilesMultipleDto = {
        folderId: 1,
        warehouseId: 1,
        files: [
          { name: 'File 1.pdf', size: 1024 },
        ],
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_CREATION_FAILED, 'Lỗi khi tạo nhiều file');

      jest.spyOn(service, 'createMultipleFiles').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.createMultipleFiles(createDto, mockUser)).rejects.toThrow(AppException);
      expect(service.createMultipleFiles).toHaveBeenCalledWith(createDto);
    });
  });

  describe('updateFile', () => {
    it('nên cập nhật file thành công', async () => {
      // Arrange
      const fileId = 1;
      const updateDto: UpdateFileDto = {
        name: 'Updated File.pdf',
      };

      jest.spyOn(service, 'updateFile').mockResolvedValue(mockFileResponse);

      // Act
      const result = await controller.updateFile(fileId, updateDto);

      // Assert
      expect(service.updateFile).toHaveBeenCalledWith(fileId, updateDto);
      expect(result.data).toEqual(mockFileResponse);
      expect(result.message).toBe('Cập nhật file thành công');
    });

    it('nên ném lỗi khi cập nhật file thất bại', async () => {
      // Arrange
      const fileId = 1;
      const updateDto: UpdateFileDto = {
        name: 'Updated File.pdf',
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_UPDATE_FAILED, 'Lỗi khi cập nhật file');

      jest.spyOn(service, 'updateFile').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.updateFile(fileId, updateDto)).rejects.toThrow(AppException);
      expect(service.updateFile).toHaveBeenCalledWith(fileId, updateDto);
    });
  });

  describe('getFileById', () => {
    it('nên lấy thông tin file theo ID thành công', async () => {
      // Arrange
      const fileId = 1;

      jest.spyOn(service, 'getFileById').mockResolvedValue(mockFileResponse);

      // Act
      const result = await controller.getFileById(fileId);

      // Assert
      expect(service.getFileById).toHaveBeenCalledWith(fileId);
      expect(result.data).toEqual(mockFileResponse);
      expect(result.message).toBe('Lấy thông tin file thành công');
    });

    it('nên ném lỗi khi lấy thông tin file thất bại', async () => {
      // Arrange
      const fileId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_NOT_FOUND, 'File không tồn tại');

      jest.spyOn(service, 'getFileById').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getFileById(fileId)).rejects.toThrow(AppException);
      expect(service.getFileById).toHaveBeenCalledWith(fileId);
    });
  });

  describe('getFiles', () => {
    it('nên lấy danh sách file với phân trang thành công', async () => {
      // Arrange
      const queryDto: QueryFileDto = {
        page: 1,
        limit: 10,
        warehouseId: 1,
      };

      jest.spyOn(service, 'getFiles').mockResolvedValue(mockPaginatedResult);

      // Act
      const result = await controller.getFiles(queryDto);

      // Assert
      expect(service.getFiles).toHaveBeenCalledWith(queryDto);
      expect(result.data).toEqual(mockPaginatedResult);
      expect(result.message).toBe('Lấy danh sách file thành công');
    });

    it('nên ném lỗi khi lấy danh sách file thất bại', async () => {
      // Arrange
      const queryDto: QueryFileDto = {
        page: 1,
        limit: 10,
      };
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_FETCH_FAILED, 'Lỗi khi lấy danh sách file');

      jest.spyOn(service, 'getFiles').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getFiles(queryDto)).rejects.toThrow(AppException);
      expect(service.getFiles).toHaveBeenCalledWith(queryDto);
    });
  });

  describe('getFilesByFolderId', () => {
    it('nên lấy danh sách file theo ID thư mục thành công', async () => {
      // Arrange
      const folderId = 1;

      jest.spyOn(service, 'getFilesByFolderId').mockResolvedValue(mockFileResponseList);

      // Act
      const result = await controller.getFilesByFolderId(folderId);

      // Assert
      expect(service.getFilesByFolderId).toHaveBeenCalledWith(folderId);
      expect(result.data).toEqual(mockFileResponseList);
      expect(result.message).toBe('Lấy danh sách file theo thư mục thành công');
    });

    it('nên ném lỗi khi lấy danh sách file theo ID thư mục thất bại', async () => {
      // Arrange
      const folderId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_FETCH_FAILED, 'Lỗi khi lấy danh sách file theo thư mục');

      jest.spyOn(service, 'getFilesByFolderId').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.getFilesByFolderId(folderId)).rejects.toThrow(AppException);
      expect(service.getFilesByFolderId).toHaveBeenCalledWith(folderId);
    });
  });

  describe('deleteFile', () => {
    it('nên xóa file thành công', async () => {
      // Arrange
      const fileId = 1;

      jest.spyOn(service, 'deleteFile').mockResolvedValue(undefined);

      // Act
      const result = await controller.deleteFile(fileId);

      // Assert
      expect(service.deleteFile).toHaveBeenCalledWith(fileId);
      expect(result.message).toBe('Xóa file thành công');
    });

    it('nên ném lỗi khi xóa file thất bại', async () => {
      // Arrange
      const fileId = 1;
      const error = new AppException(BUSINESS_ERROR_CODES.FILE_DELETE_FAILED, 'Lỗi khi xóa file');

      jest.spyOn(service, 'deleteFile').mockRejectedValue(error);

      // Act & Assert
      await expect(controller.deleteFile(fileId)).rejects.toThrow(AppException);
      expect(service.deleteFile).toHaveBeenCalledWith(fileId);
    });
  });
});
