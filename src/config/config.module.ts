import { Module, Global } from '@nestjs/common';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { ConfigService } from './config.service';
import { validationSchema } from './validation.schema';
import { CONFIG_CONSTANTS, Environment } from './constants';

/**
 * Module cung cấp cấu hình cho ứng dụng
 */
@Global()
@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      envFilePath: (() => {
        const nodeEnv = process.env.NODE_ENV as Environment;
        const envFiles = [
          CONFIG_CONSTANTS.ENV_FILES[nodeEnv] || CONFIG_CONSTANTS.ENV_FILES.default,
          CONFIG_CONSTANTS.ENV_FILES.default
        ];
        console.log('NODE_ENV:', nodeEnv);
        console.log('Loading env files:', envFiles);
        return envFiles;
      })(),
      validationSchema: validationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: false,
      },
      expandVariables: true,
      cache: true,
    }),
  ],
  providers: [ConfigService],
  exports: [ConfigService, NestConfigModule],
})
export class ConfigModule {}
