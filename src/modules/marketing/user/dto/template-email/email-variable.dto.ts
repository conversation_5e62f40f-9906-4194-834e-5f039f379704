import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsEnum, IsOptional, IsBoolean, MaxLength, Matches } from 'class-validator';

/**
 * DTO cho định nghĩa biến trong email template
 */
export class EmailVariableDto {
  /**
   * Tên biến (không có dấu cách, chỉ chữ cái, số và _)
   * @example "customer_name"
   */
  @ApiProperty({
    description: 'Tên biến (không có dấu cách, chỉ chữ cái, số và _)',
    example: 'customer_name',
    pattern: '^[a-zA-Z_][a-zA-Z0-9_]*$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z_][a-zA-Z0-9_]*$/, {
    message: 'Tên biến chỉ được chứa chữ cái, số và dấu gạch dưới, bắt đầu bằng chữ cái hoặc _'
  })
  name: string;

  /**
   * Loại dữ liệu của biến
   * @example "TEXT"
   */
  @ApiProperty({
    description: 'Loại dữ liệu của biến',
    example: 'TEXT',
    enum: ['TEXT', 'NUMBER', 'DATE', 'URL', 'IMAGE'],
  })
  @IsEnum(['TEXT', 'NUMBER', 'DATE', 'URL', 'IMAGE'])
  type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';

  /**
   * Giá trị mặc định
   * @example "Khách hàng"
   */
  @ApiProperty({
    description: 'Giá trị mặc định',
    example: 'Khách hàng',
    required: false,
  })
  @IsOptional()
  @IsString()
  defaultValue?: string;

  /**
   * Biến có bắt buộc hay không
   * @example true
   */
  @ApiProperty({
    description: 'Biến có bắt buộc hay không',
    example: true,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  required?: boolean;

  /**
   * Mô tả biến
   * @example "Tên khách hàng sẽ được hiển thị trong email"
   */
  @ApiProperty({
    description: 'Mô tả biến',
    example: 'Tên khách hàng sẽ được hiển thị trong email',
    required: false,
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;
}
