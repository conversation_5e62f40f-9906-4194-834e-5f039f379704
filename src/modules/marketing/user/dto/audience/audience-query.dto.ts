import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsInt, IsOptional, IsPhoneNumber, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query parameters khi lấy danh sách audience
 */
export class AudienceQueryDto extends QueryDto {

  /**
   * Tìm kiếm theo email
   * @example "example.com"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo email',
    example: 'example.com',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Email phải là chuỗi' })
  email?: string;

  /**
   * Tìm kiếm theo số điện thoại
   * @example "+84"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo số điện thoại',
    example: '+84',
    required: false,
  })
  @IsOptional()
  @IsString({ message: '<PERSON><PERSON> điện thoại phải là chuỗi' })
  phone?: string;

  /**
   * Tìm kiếm theo tag ID
   * @example 1
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tag ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt({ message: 'Tag ID phải là số nguyên' })
  @Type(() => Number)
  tagId?: number;

  /**
   * Tìm kiếm theo tên trường tùy chỉnh
   * @example "address"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên trường tùy chỉnh',
    example: 'address',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên trường tùy chỉnh phải là chuỗi' })
  customFieldName?: string;

  /**
   * Tìm kiếm theo giá trị trường tùy chỉnh
   * @example "Hanoi"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo giá trị trường tùy chỉnh',
    example: 'Hanoi',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Giá trị trường tùy chỉnh phải là chuỗi' })
  customFieldValue?: string;


}
