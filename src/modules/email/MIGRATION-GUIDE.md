# EmailPlaceholderService Migration Guide

## Tóm tắt thay đổi

EmailPlaceholderService đã được cập nhật để:
1. ✅ Sử dụng `EmailSystemQueueService` với `addHighPriorityEmailSystemJob()`
2. ✅ Thay thế `templateId` bằng `CategoryTemplateAutoEnum`
3. ✅ Thêm tham số `to: string` cho tất cả method
4. ✅ Loại bỏ dependency vào `data.EMAIL`

## Pattern Migration

### ❌ Cũ (Before):
```typescript
async sendExampleEmail(
  data: Partial<Record<keyof typeof ExamplePlaceholder, string | null | undefined>>
): Promise<string | number> {
  try {
    // Kiểm tra data
    if (!data) {
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không có dữ liệu');
    }

    // Kiểm tra email từ data
    if (!data.EMAIL) {
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Thiếu email');
    }

    this.logger.log(`Gửi email đến: ${data.EMAIL}`);

    // Template ID
    const templateId = 'example-template';

    // Sanitize data
    const cleanData = this.sanitizeData(data as Record<string, any>);

    // Validate fields including EMAIL
    this.validateEssentialFields(cleanData, ['EMAIL', 'NAME']);

    // Gửi qua EmailClientService
    return await this.emailService.sendTemplateEmail(
      cleanData.EMAIL,
      templateId,
      cleanData
    );
  } catch (error) {
    // Error handling
  }
}
```

### ✅ Mới (After):
```typescript
async sendExampleEmail(
  to: string,
  data: Partial<Record<keyof typeof ExamplePlaceholder, string | null | undefined>>
): Promise<string | number> {
  try {
    // Kiểm tra email parameter
    if (!to) {
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Thiếu email');
    }

    // Kiểm tra data
    if (!data) {
      throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Không có dữ liệu');
    }

    this.logger.log(`Gửi email đến: ${to}`);

    // Sanitize data
    const cleanData = this.sanitizeData(data as Record<string, any>);

    // Validate fields (không cần EMAIL nữa)
    this.validateEssentialFields(cleanData, ['NAME']);

    // Tạo EmailSystemJobDto
    const emailData: EmailSystemJobDto = {
      category: CategoryTemplateAutoEnum.APPROPRIATE_CATEGORY,
      to: to,
      data: cleanData,
    };

    // Gửi qua EmailSystemQueueService
    return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(emailData);
  } catch (error) {
    // Error handling
  }
}
```

## Template ID → Category Mapping

| Template ID | CategoryTemplateAutoEnum |
|-------------|--------------------------|
| `affiliate-withdraw-rejected-template` | `AFFILIATE_WITHDRAWAL_REJECTED` |
| `account-registration-success-template` | `ACCOUNT_REGISTRATION_SUCCESSFUL` |
| `two-fa-verification-template` | `ACCOUNT_2FA` |
| `affiliate-rank-up-template` | `AFFILIATE_RANK_CHANGE_INCREASED` |
| `ticket-low-rating-template` | `OPERATION_TICKET_LOW_RATING` |
| `payment-success-template` | `PAYMENT_SUCCESSFUL` |
| `email-verification-template` | `ACCOUNT_VERIFICATION_EMAIL` |
| `feature-suggestion-thank-you-template` | `OTHER_THANK_FOR_FEATURE_SUGGESTION` |
| `discount-campaign-template` | `MARKETING_MONTHLY_DISCOUNT` |
| `assistant-inactive-template` | `OPERATION_ASSISTANT_INACTIVE` |
| `employee-feature-suggestion-template` | `EMPLOYEE_OPERATION_NEW_FEATURE_SUGGESTION` |
| `payment-gateway-integration-template` | `INTEGRATION_PAYMENT_GATEWAY` |
| `website-integration-template` | `INTEGRATION_WEBSITE` |
| `bad-experience-apology-template` | `OPERATION_TICKET_SORRY_BAD_EXPERIENCE` |
| `rule-contract-reminder-template` | `CONTRACT_REMINDER_TO_SIGN` |
| `affiliate-priority-benefit-template` | `AFFILIATE_PRIORITY_BENEFITS` |
| `affiliate-rank-up-reward-template` | `AFFILIATE_GIFT_FUNCTION_STRATEGY` |
| `facebook-integration-template` | `INTEGRATION_FACEBOOK` |
| `new-rule-contract-processing-template` | `EMPLOYEE_OPERATION_CONTRACT_NEEDS_PROCESSING` |
| `business-info-update-processing-template` | `EMPLOYEE_OPERATION_EDIT_BUSINESS_INFO_REQUEST` |
| `new-invoice-processing-template` | `EMPLOYEE_NEW_INVOICE_MUST_EXPORT` |
| `affiliate-discount-code-template` | `AFFILIATE_GIVE_DISCOUNT_CODE` |

## Các bước thực hiện

### 1. Cập nhật method signature
```typescript
// Thêm tham số 'to: string' đầu tiên
async methodName(
  to: string,  // ← Thêm này
  data: Partial<Record<keyof typeof SomePlaceholder, string | null | undefined>>
): Promise<string | number>
```

### 2. Cập nhật validation
```typescript
// Kiểm tra 'to' parameter thay vì 'data.EMAIL'
if (!to) {
  throw new AppException(ErrorCode.INTERNAL_SERVER_ERROR, 'Thiếu địa chỉ email');
}

// Loại bỏ 'EMAIL' khỏi validateEssentialFields
this.validateEssentialFields(cleanData, ['NAME']); // Không có 'EMAIL'
```

### 3. Cập nhật logging
```typescript
// Sử dụng 'to' parameter
this.logger.log(`Chuẩn bị gửi email đến: ${to}`);
```

### 4. Thay thế templateId bằng category
```typescript
// Xóa dòng này:
// const templateId = 'some-template';

// Thêm EmailSystemJobDto:
const emailData: EmailSystemJobDto = {
  category: CategoryTemplateAutoEnum.APPROPRIATE_CATEGORY,
  to: to,
  data: cleanData,
};
```

### 5. Thay thế service call
```typescript
// Thay thế:
// return await this.emailService.sendTemplateEmail(cleanData.EMAIL, templateId, cleanData);

// Bằng:
return await this.emailSystemQueueService.addHighPriorityEmailSystemJob(emailData);
```

## Cách sử dụng mới

```typescript
// Trước đây:
await emailPlaceholderService.sendAccountRegistrationSuccess({
  EMAIL: '<EMAIL>',
  NAME: 'John Doe',
  LOGIN_URL: 'https://example.com/login'
});

// Bây giờ:
await emailPlaceholderService.sendAccountRegistrationSuccess(
  '<EMAIL>',  // ← Email riêng biệt
  {
    NAME: 'John Doe',
    LOGIN_URL: 'https://example.com/login'
    // Không cần EMAIL trong data nữa
  }
);
```

## Lợi ích

1. **Tách biệt rõ ràng**: Email người nhận và dữ liệu template
2. **Linh hoạt hơn**: Có thể gửi cùng data đến nhiều email khác nhau
3. **Queue system**: Sử dụng high-priority queue cho tất cả email
4. **Category-based**: Sử dụng enum thay vì string template ID
5. **Type safety**: Tốt hơn với TypeScript

## Status

✅ **Đã hoàn thành:**
- sendAffiliateWithdrawRejected()
- sendAccountRegistrationSuccess()
- sendTwoFAVerification()
- sendAffiliateRankUp()
- sendTicketLowRating()
- sendPaymentSuccess()
- sendEmailVerification()
- sendFeatureSuggestionThankYou()
- sendDiscountCampaign()
- sendAssistantInactive()

🔄 **Cần áp dụng pattern cho:**
- Tất cả các method còn lại trong EmailPlaceholderService (khoảng 30+ methods)

## Lưu ý

- Tất cả method đều sử dụng `addHighPriorityEmailSystemJob()` để đảm bảo độ ưu tiên cao
- Processor ở worker sẽ nhận job từ queue `EMAIL_SYSTEM` với job name `SEND_TEMPLATE_EMAIL`
- Data structure trong queue: `{ category, to, data }`
