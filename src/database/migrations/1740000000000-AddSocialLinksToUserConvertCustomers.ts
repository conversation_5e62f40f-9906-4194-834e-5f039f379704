import { MigrationInterface, QueryRunner } from 'typeorm';

/**
 * Migration để thêm các cột social links vào bảng user_convert_customers
 * C<PERSON>c cột: facebook_link, twitter_link, linkedin_link, zalo_link, website_link
 */
export class AddSocialLinksToUserConvertCustomers1740000000000 implements MigrationInterface {
  /**
   * Thực hiện migration
   * @param queryRunner QueryRunner
   */
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Thêm cột facebook_link
    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      ADD COLUMN IF NOT EXISTS "facebook_link" VARCHAR(500) NULL
    `);

    // Thêm cột twitter_link
    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      ADD COLUMN IF NOT EXISTS "twitter_link" VARCHAR(500) NULL
    `);

    // Thêm cột linkedin_link
    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      ADD COLUMN IF NOT EXISTS "linkedin_link" VARCHAR(500) NULL
    `);

    // Thêm cột zalo_link
    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      ADD COLUMN IF NOT EXISTS "zalo_link" VARCHAR(500) NULL
    `);

    // Thêm cột website_link
    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      ADD COLUMN IF NOT EXISTS "website_link" VARCHAR(500) NULL
    `);

    // Thêm comment cho các cột
    await queryRunner.query(`
      COMMENT ON COLUMN "user_convert_customers"."facebook_link" 
      IS 'Link Facebook của khách hàng'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "user_convert_customers"."twitter_link" 
      IS 'Link Twitter của khách hàng'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "user_convert_customers"."linkedin_link" 
      IS 'Link LinkedIn của khách hàng'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "user_convert_customers"."zalo_link" 
      IS 'Link Zalo của khách hàng'
    `);

    await queryRunner.query(`
      COMMENT ON COLUMN "user_convert_customers"."website_link" 
      IS 'Link Website của khách hàng'
    `);
  }

  /**
   * Rollback migration
   * @param queryRunner QueryRunner
   */
  public async down(queryRunner: QueryRunner): Promise<void> {
    // Xóa các cột social links
    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      DROP COLUMN IF EXISTS "facebook_link"
    `);

    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      DROP COLUMN IF EXISTS "twitter_link"
    `);

    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      DROP COLUMN IF EXISTS "linkedin_link"
    `);

    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      DROP COLUMN IF EXISTS "zalo_link"
    `);

    await queryRunner.query(`
      ALTER TABLE "user_convert_customers" 
      DROP COLUMN IF EXISTS "website_link"
    `);
  }
}
