import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder, In } from 'typeorm';
import { Url } from '../entities/url.entity';
import { SqlHelper } from '@common/helpers/sql.helper';
import { PaginatedResult } from '@common/response/api-response-dto';
import { FindAllUrlDto } from '../user/dto/find-all-url.dto';

@Injectable()
export class UrlRepository extends Repository<Url> {

  private sqlHelper: SqlHelper;
  private readonly logger = new Logger(UrlRepository.name);

  constructor(private dataSource: DataSource) {
    super(Url, dataSource.createEntityManager());
    this.sqlHelper = new SqlHelper(dataSource, { enableLogging: true });
  }

  // Phương thức searchUrls đã được tích hợp vào findUrlsByOwner

  /**
   * Lấy URL theo ID
   * @param id ID của URL
   * @returns URL nếu tìm thấy, null nếu không tìm thấy
   */
  async findUrlById(id: string): Promise<Url | null> {
    try {
      this.logger.log(`Finding URL with ID: ${id}`);

      // Thử sử dụng truy vấn SQL trực tiếp
      const rawQuery = `
        SELECT id, url, title, content, type, tags, owned_by, created_at, updated_at, is_active
        FROM url_data
        WHERE id = $1
      `;

      const rawResults = await this.dataSource.query(rawQuery, [id]);
      this.logger.log(`Raw SQL direct query results: ${JSON.stringify(rawResults)}`);

      if (rawResults && rawResults.length > 0) {
        const rawItem = rawResults[0];
        this.logger.log(`Found URL with raw SQL: ${JSON.stringify(rawItem)}`);

        // Chuyển đổi kết quả thành entity
        const url = new Url();
        url.id = rawItem.id;
        url.url = rawItem.url || '';
        url.title = rawItem.title || '';
        url.content = rawItem.content || '';
        url.type = rawItem.type || '';
        url.tags = rawItem.tags || [];
        url.ownedBy = Number(rawItem.owned_by || 0);
        url.createdAt = rawItem.created_at ? Number(rawItem.created_at) : 0;
        url.updatedAt = rawItem.updated_at ? Number(rawItem.updated_at) : 0;
        url.isActive = rawItem.is_active !== undefined ? rawItem.is_active : true;
        url.urlEmbedding = null;
        url.titleEmbedding = null;
        url.contentEmbedding = null;

        this.logger.log(`Converted URL entity: ${JSON.stringify(url)}`);
        return url;
      }

      // Nếu không tìm thấy bằng truy vấn trực tiếp, thử sử dụng SqlHelper
      this.logger.log(`URL not found with direct SQL, trying SqlHelper`);

      // Sử dụng SqlHelper để thực hiện truy vấn
      const results = await this.sqlHelper.select(
        'url_data',
        ['*'],  // Lấy tất cả các cột
        [{ condition: 'id = :id', params: { id } }],
        [],
        [],
        [],
        undefined,
        undefined,
        { raw: true },
        Url
      );

      // Debug log - hiển thị kết quả trả về từ database
      this.logger.log(`SqlHelper results: ${JSON.stringify(results)}`);

      // Kiểm tra xem kết quả có rỗng không
      if (results.length === 0 || Object.keys(results[0]).length === 0) {
        this.logger.warn(`URL with ID: ${id} not found or empty result`);
        return null;
      }

      if (results.length > 0) {
        // Debug log
        this.logger.log(`Found URL with ID: ${id}`);
        this.logger.debug(`URL details: ${JSON.stringify(results[0])}`);

        // Chuyển đổi kết quả thành entity
        const item: any = results[0];

        // Kiểm tra xem item có dữ liệu không
        if (!item || Object.keys(item).length === 0) {
          this.logger.warn(`URL with ID: ${id} returned empty object`);
          return null;
        }

        // Debug log - hiển thị các trường của item
        this.logger.log(`Item fields: ${Object.keys(item).join(', ')}`);
        this.logger.log(`Item ownedBy: ${item.ownedBy}, owned_by: ${item.owned_by}`);

        const url = new Url();
        url.id = item.id || id; // Sử dụng ID được truyền vào nếu không có trong kết quả
        url.url = item.url || '';
        url.title = item.title || '';
        url.content = item.content || '';
        url.type = item.type || '';
        url.tags = item.tags || [];

        // Đảm bảo ownedBy luôn là số
        // Trong database, cột tên là owned_by nhưng trong entity là ownedBy
        if (item.owned_by !== undefined) {
          url.ownedBy = Number(item.owned_by);
          this.logger.log(`Using owned_by field: ${item.owned_by} -> ${url.ownedBy}`);
        } else if (item.ownedBy !== undefined) {
          url.ownedBy = Number(item.ownedBy);
          this.logger.log(`Using ownedBy field: ${item.ownedBy} -> ${url.ownedBy}`);
        } else {
          // Nếu không tìm thấy cả hai trường, thử tìm trong các trường khác
          const ownerField = Object.keys(item).find(key =>
            key.toLowerCase().includes('own') || key.toLowerCase().includes('user'));

          if (ownerField) {
            url.ownedBy = Number(item[ownerField]);
            this.logger.log(`Using ${ownerField} field: ${item[ownerField]} -> ${url.ownedBy}`);
          } else {
            // Nếu vẫn không tìm thấy, hiển thị tất cả các trường để debug
            this.logger.warn(`No owner field found. Available fields: ${Object.keys(item).join(', ')}`);
            this.logger.warn(`Full item data: ${JSON.stringify(item)}`);
            this.logger.warn(`URL with ID: ${id} has no owner information, returning null`);
            return null; // Trả về null thay vì gán giá trị mặc định
          }
        }

        url.createdAt = item.createdAt;
        url.updatedAt = item.updatedAt;

        // Xử lý trường isActive
        if (item.is_active !== undefined) {
          url.isActive = item.is_active;
          this.logger.log(`Using is_active field: ${item.is_active}`);
        } else if (item.isActive !== undefined) {
          url.isActive = item.isActive;
          this.logger.log(`Using isActive field: ${item.isActive}`);
        } else {
          url.isActive = true; // Giá trị mặc định
          this.logger.log(`No isActive field found, using default: true`);
        }

        url.urlEmbedding = null;
        url.titleEmbedding = null;
        url.contentEmbedding = null;

        return url;
      }

      this.logger.log(`URL with ID: ${id} not found`);
      return null;
    } catch (error) {
      this.logger.error(`Error finding URL by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách URL theo người sở hữu và tìm kiếm theo từ khóa (nếu có)
   * @param ownerId ID của người sở hữu
   * @param queryParams Tham số phân trang, sắp xếp và tìm kiếm
   * @returns Danh sách URL đã phân trang
   */
  async findUrlsByOwner(ownerId: number | null, queryParams: any): Promise<PaginatedResult<Url>> {
    try {
      this.logger.log(`Finding URLs for owner: ${ownerId} with params: ${JSON.stringify(queryParams)}`);

      // Tạo kết quả phân trang sử dụng helper function
      const result = await this.sqlHelper.getPaginatedData(this, queryParams, {
        alias: 'url',
        // Đảm bảo sử dụng đúng tên trường trong entity
        selectFields: ['id', 'url', 'title', 'content', 'type', 'tags', 'ownedBy', 'createdAt', 'updatedAt', 'isActive'],
        searchFields: ['url', 'title', 'content'], // Các trường tìm kiếm khi dùng search
        customize: (qb: SelectQueryBuilder<Url>) => {
          // Lọc theo người sở hữu nếu có
          if (ownerId !== null) {
            qb.where('url.owned_by = :ownerId', { ownerId });
          }

          // Lọc theo userId nếu có trong queryParams
          if (queryParams.userId) {
            qb.andWhere('url.owned_by = :userId', { userId: queryParams.userId });
          }

          // Lọc theo trạng thái kích hoạt nếu có
          if (queryParams.isActive !== undefined) {
            qb.andWhere('url.is_active = :isActive', { isActive: queryParams.isActive });
          }

          // Tìm kiếm theo keyword nếu có
          if (queryParams.keyword) {
            qb.andWhere(
              '(url.title ILIKE :keyword OR url.content ILIKE :keyword OR url.url ILIKE :keyword)',
              { keyword: `%${queryParams.keyword}%` }
            );
          }

          // Lọc theo loại URL nếu có
          if (queryParams.type) {
            qb.andWhere('url.type = :type', { type: queryParams.type });
          }

          // Lọc theo tags nếu có
          if (queryParams.tags && queryParams.tags.length > 0) {
            // Hỗ trợ 3 format tags:
            // 1. String exact: url.tags = '"tag_value"'::jsonb
            // 2. String contains: url.tags::text ILIKE '%tag_value%'
            // 3. Array contains: url.tags @> ["tag_value"]
            const tagConditions = queryParams.tags.map((tag: string, index: number) => {
              return `(
                url.tags = :tagString${index}::jsonb OR
                url.tags::text ILIKE :tagContains${index} OR
                url.tags @> :tagArray${index}::jsonb
              )`;
            });

            const tagParams: any = {};
            queryParams.tags.forEach((tag: string, index: number) => {
              tagParams[`tagString${index}`] = JSON.stringify(tag); // For exact string: "Enzo Ferrari"
              tagParams[`tagContains${index}`] = `%${tag}%`; // For substring: %Enzo Ferrari%
              tagParams[`tagArray${index}`] = JSON.stringify([tag]); // For array: ["Enzo Ferrari"]
            });

            qb.andWhere(`(${tagConditions.join(' OR ')})`, tagParams);
          }

          return qb;
        }
      });

      // Debug log
      this.logger.log(`Found ${result.items.length} URLs for owner ${ownerId}`);
      if (result.items.length > 0) {
        this.logger.debug(`First URL: ${JSON.stringify(result.items[0])}`);
      }

      // Đảm bảo các trường của URL được chuyển đổi đúng
      const processedItems = result.items.map(item => {
        // Đảm bảo các trường cần thiết đều có mặt
        const url = new Url();
        url.id = item.id;
        url.url = item.url;
        url.title = item.title || '';
        url.content = item.content || '';
        url.type = item.type || '';
        url.tags = item.tags || [];
        // Đảm bảo ownedBy luôn là số
        url.ownedBy = Number(item.ownedBy || 0);
        url.createdAt = item.createdAt;
        url.updatedAt = item.updatedAt;
        url.isActive = item.isActive !== undefined ? item.isActive : true;
        url.urlEmbedding = null;
        url.titleEmbedding = null;
        url.contentEmbedding = null;

        return url;
      });

      return {
        items: processedItems,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Error finding URLs by owner: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm kiếm URL theo từ khóa
   * @param keyword Từ khóa tìm kiếm
   * @param limit Số lượng kết quả tối đa
   * @returns Danh sách URL phù hợp
   */
  async searchUrls(keyword: string, limit: number = 10): Promise<Url[]> {
    try {
      this.logger.log(`Searching URLs with keyword: ${keyword}, limit: ${limit}`);

      // Sử dụng SqlHelper để thực hiện truy vấn tìm kiếm
      const whereConditions = [
        { condition: 'title ILIKE :keyword', params: { keyword: `%${keyword}%` } },
        { condition: 'content ILIKE :keyword', params: { keyword: `%${keyword}%` } },
        { condition: 'url ILIKE :keyword', params: { keyword: `%${keyword}%` } }
      ];

      // Tạo câu lệnh SQL với điều kiện OR
      const orConditions = whereConditions.map(c => c.condition).join(' OR ');
      const params = {};
      whereConditions.forEach(c => Object.assign(params, c.params));

      const results = await this.sqlHelper.select(
        'url_data',
        [
          'id',
          'url',
          'title',
          'content',
          'type',
          'tags',
          'owned_by', // Đảm bảo lấy đúng tên cột
          'created_at as "createdAt"',
          'updated_at as "updatedAt"',
          'is_active as "isActive"'
        ],
        [{ condition: `(${orConditions})`, params }],
        [],
        [{ column: 'created_at', direction: 'DESC' }],
        [],
        undefined,
        { limit },
        { raw: true },
        Url
      );

      // Debug log
      this.logger.log(`Found ${results.length} URLs matching keyword: ${keyword}`);
      if (results.length > 0) {
        this.logger.debug(`First search result: ${JSON.stringify(results[0])}`);
      }

      // Đảm bảo các trường của URL được chuyển đổi đúng
      return results.map(item => {
        const url = new Url();
        url.id = item.id;
        url.url = item.url;
        url.title = item.title || '';
        url.content = item.content || '';
        url.type = item.type || '';
        url.tags = item.tags || [];
        // Đảm bảo ownedBy luôn là số
        url.ownedBy = Number(item.ownedBy || 0);
        url.createdAt = item.createdAt;
        url.updatedAt = item.updatedAt;
        url.isActive = item.isActive !== undefined ? item.isActive : true;
        url.urlEmbedding = null;
        url.titleEmbedding = null;
        url.contentEmbedding = null;

        return url;
      });
    } catch (error) {
      this.logger.error(`Error searching URLs: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Lấy danh sách URL theo danh sách ID
   * @param ids Danh sách ID của URL
   * @returns Danh sách URL
   */
  async findUrlsByIds(ids: string[]): Promise<Url[]> {
    try {
      this.logger.log(`Finding URLs with IDs: ${ids.join(', ')}`);

      if (!ids || ids.length === 0) {
        return [];
      }

      const urls = await this.find({
        where: { id: In(ids) },
        order: { createdAt: 'DESC' }
      });

      // Debug log
      this.logger.log(`Found ${urls.length} URLs by IDs`);
      if (urls.length > 0) {
        this.logger.debug(`First URL: ${JSON.stringify(urls[0])}`);
      }

      // Đảm bảo các trường của URL được đầy đủ
      return urls.map(item => {
        // Đảm bảo các trường null được xử lý đúng
        if (!item.title) item.title = '';
        if (!item.content) item.content = '';
        if (!item.type) item.type = '';
        if (!item.tags) item.tags = [];
        if (typeof item.ownedBy === 'string') {
          item.ownedBy = Number(item.ownedBy);
        }

        // Đảm bảo isActive có giá trị
        if (item.isActive === undefined) {
          // Kiểm tra các trường khác có thể chứa giá trị isActive
          if ((item as any).is_active !== undefined) {
            item.isActive = (item as any).is_active;
          } else {
            item.isActive = true; // Giá trị mặc định
          }
        }

        return item;
      });
    } catch (error) {
      this.logger.error(`Error finding URLs by IDs: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Đếm số URLs đã lưu theo user và khoảng thời gian
   * @param userId ID của user
   * @param startTime Thời gian bắt đầu (timestamp)
   * @param endTime Thời gian kết thúc (timestamp)
   * @returns Số lượng URLs đã lưu
   */
  async countUrlsByUserAndTimeRange(
    userId: number,
    startTime: number,
    endTime: number
  ): Promise<number> {
    try {
      this.logger.log(`Counting URLs for user ${userId} between ${startTime} and ${endTime}`);

      const count = await this.createQueryBuilder('url')
        .where('url.ownedBy = :userId', { userId })
        .andWhere('url.createdAt >= :startTime', { startTime })
        .andWhere('url.createdAt <= :endTime', { endTime })
        .andWhere('url.isActive = :isActive', { isActive: true })
        .getCount();

      this.logger.log(`Found ${count} URLs for user ${userId} in time range`);
      return count;
    } catch (error) {
      this.logger.error(`Error counting URLs by user and time range: ${error.message}`, error.stack);
      throw error;
    }
  }
}
