import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

/**
 * Entity đại diện cho bảng user_settings trong cơ sở dữ liệu
 * Lưu trữ các cài đặt người dùng bao gồm theme (dạng JSON) và timezone
 */
@Entity('user_settings')
export class UserSettings {
  /**
   * Unique identifier for the user settings record
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Foreign key referencing the users table
   */
  @Column({ name: 'user_id', type: 'integer', unique: true })
  @Index('idx_user_settings_user_id')
  userId: number;

  /**
   * User interface theme stored as JSON (e.g., {"mode": "light", "color": "blue"})
   */
  @Column({ 
    name: 'theme', 
    type: 'json', 
    default: () => "''{\"mode\": \"light\"}''" 
  })
  theme: Record<string, any>;

  /**
   * User-selected timezone (e.g., UTC, Asia/Ho_Chi_Minh)
   */
  @Column({ 
    name: 'timezone', 
    type: 'varchar', 
    length: 50, 
    default: 'UTC' 
  })
  timezone: string;

  /**
   * Timestamp when the record was created (in milliseconds)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  createdAt: number;

  /**
   * Timestamp when the record was last updated (in milliseconds)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
  })
  updatedAt: number;
}
