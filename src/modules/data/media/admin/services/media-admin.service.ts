import {Injectable, Logger} from '@nestjs/common';
import { Media } from '../../entities/media.entity';
import {ApiResponseDto, PaginatedResult} from "@common/response";
import {MediaQueryDto} from "@/modules/data/media/dto/media-query.dto";
import {MediaRepository} from "@modules/data/media/repositories";
import {S3Service} from "@shared/services/s3.service";
import {AppException, ErrorCode} from "@/common";
import {AdminMediaResponseDto} from "@/modules/data/media/dto/media-admin.dto";
import {DataSource, In, Repository} from "typeorm";
import {InjectRepository} from "@nestjs/typeorm";
import {User} from "@modules/user/entities";
import { AgentMediaRepository } from '@modules/agent/repositories/agent-media.repository';
import { MediaMapper } from '../../mappers/media-admin.mapper';
import { UserRepository } from '@/modules/user/repositories';
import { CdnService } from '@/shared/services/cdn.service';
import { MediaValidationHelper } from '../../helpers/validation.helper';
import { MEDIA_ERROR_CODES } from '../../exception';
import { AgentMedia } from '@/modules/agent/entities/agents-media.entity';
import { chunk } from 'lodash';
import { MediaResponseDto } from '../../dto/media-user.dto';
import { MediaStatusEnum } from '../../enums/media-status.enum';
import { Transactional } from 'typeorm-transactional';
import { DeleteAgentMediaDto } from '../../dto/delete-agent-media.dto';

@Injectable()
export class MediaAdminService {
  private log = new Logger(MediaAdminService.name);

  constructor(
      private readonly mediaRepository: MediaRepository,
      private readonly s3Service: S3Service,
      private readonly agentMediaRepository: AgentMediaRepository,
      private readonly userRepository: UserRepository,
      private readonly cdnService: CdnService,
      private readonly mediaValidationHelper: MediaValidationHelper,
      private readonly dataSource: DataSource,
  ) {}

  /**
   * Lấy danh sách media của người dùng
   * @param query Tham số truy vấn
   * @param isAdmin
   * @returns Danh sách media với phân trang
   */
  async findAllForAdmin(
      query: MediaQueryDto,
      isAdmin: boolean|undefined,
  ): Promise<PaginatedResult<AdminMediaResponseDto>> {

    this.mediaValidationHelper.isAdmin(isAdmin);
     // Lấy dữ liệu phân trang từ repository
    const paginatedResult = await this.mediaRepository.findAllForAdmin(query);
    const updatedMediaList = await MediaMapper.toAdminList(paginatedResult.items, this.cdnService);


    // Trả về kết quả phân trang đã xử lý
    return{
      items: updatedMediaList,
      meta: paginatedResult.meta,
  };
  }

  /**
   * Tìm media theo ID
   * @param id ID của media
   * @param isAdmin để check quyền admin
   * @returns Media nếu tìm thấy và là của người dùng
   */
  async findByIdForAdmin(
      id: string,
      isAdmin: boolean|undefined,
  ): Promise<AdminMediaResponseDto> {
    this.mediaValidationHelper.isAdmin(isAdmin);
    const media = await this.mediaRepository.findByIdConfig(id) as Media;

    this.mediaValidationHelper.validateMediaExists(media, id);


    return (await MediaMapper.toAdminDetail(media, this.s3Service)) ;
  }

  /**
   * Xóa liên kết nhiều agent với một media
   * @param dto DTO chứa thông tin xóa liên kết (mediaId và danh sách agentIds)
   * @param isAdmin để check quyền admin
   */
  @Transactional()
  async deleteAgentMediaByAdmin(
    dto: DeleteAgentMediaDto,
    isAdmin: boolean | undefined
  ): Promise<{ deletedIds: string[]; skippedIds: string[]; failedIds: { id: string; reason: string }[] }> {
    this.mediaValidationHelper.isAdmin(isAdmin);

    // Kiểm tra mediaId có tồn tại không
    if (!dto.mediaId) {
      throw new AppException(
        MEDIA_ERROR_CODES.BAD_REQUEST,
        'mediaId không được để trống'
      );
    }

    // Nếu không có agentIds, trả về kết quả trống
    if (!dto.agentIds || dto.agentIds.length === 0) {
      return {
        deletedIds: [],
        skippedIds: [],
        failedIds: []
      };
    }

    // Xóa nhiều agent khỏi một media
    return this.deleteAgentsFromMedia(dto.mediaId, dto.agentIds);
  }

  /**
   * Xóa nhiều agent khỏi một media
   * @param mediaId ID của media
   * @param agentIds Danh sách ID của các agent cần xóa liên kết
   */
  private async deleteAgentsFromMedia(
    mediaId: string,
    agentIds: string[]
  ): Promise<{ deletedIds: string[]; skippedIds: string[]; failedIds: { id: string; reason: string }[] }> {
    const deletedIds: string[] = [];
    const skippedIds: string[] = [];
    const failedIds: { id: string; reason: string }[] = [];

    // Kiểm tra media có tồn tại không
    const media = await this.mediaRepository.findOne({
      where: { id: mediaId },
      select: ['id'],
    });

    if (!media) {
      return {
        deletedIds: [],
        skippedIds: [],
        failedIds: [{ id: mediaId, reason: 'Media không tồn tại' }]
      };
    }

    // Xóa từng liên kết agent-media
    for (const agentId of agentIds) {
      try {
        const result = await this.agentMediaRepository.delete({
          mediaId,
          agentId
        });

        if (result.affected && result.affected > 0) {
          deletedIds.push(agentId);
        } else {
          skippedIds.push(agentId);
        }
      } catch (error) {
        this.log.error(`Lỗi khi xóa liên kết agent ${agentId} với media ${mediaId}: ${error.message}`, error.stack);
        failedIds.push({ id: agentId, reason: error?.message ?? 'Unknown error' });
      }
    }

    return { deletedIds, skippedIds, failedIds };
  }

  /**
   * Xóa tất cả liên kết của một media với các agent
   * @param mediaId ID của media
   * @param isAdmin để check quyền admin
   */
  @Transactional()
  async deleteAllAgentMediaByAdmin(
    mediaId: string,
    isAdmin: boolean | undefined
  ): Promise<{ deletedCount: number; mediaId: string }> {
    this.mediaValidationHelper.isAdmin(isAdmin);

    // Kiểm tra media có tồn tại không
    const media = await this.mediaRepository.findOne({
      where: { id: mediaId },
      select: ['id'],
    });

    if (!media) {
      throw new AppException(
        MEDIA_ERROR_CODES.NOT_FOUND,
        `Media với id ${mediaId} không tồn tại`
      );
    }

    try {
      // Xóa tất cả liên kết của media này với các agent
      const result = await this.agentMediaRepository.delete({ mediaId });

      return {
        deletedCount: result.affected || 0,
        mediaId
      };
    } catch (error) {
      this.log.error(`Lỗi khi xóa tất cả liên kết của media ${mediaId}: ${error.message}`, error.stack);
      throw new AppException(
        MEDIA_ERROR_CODES.BAD_REQUEST,
        `Lỗi khi xóa liên kết: ${error.message}`
      );
    }
  }

  /**
   * Xóa mềm nhiều media theo danh sách ID (cập nhật trạng thái thành DELETED)
   * @param mediaIds Danh sách ID của media
   * @param isAdmin để check quyền admin
   * @returns Danh sách các ID đã xóa thành công và các lỗi nếu có
   */
  @Transactional()
  async deleteManyMediaByAdmin(
    mediaIds: string[],
    isAdmin: boolean | undefined,
  ): Promise<{
    deletedIds: string[],
    skippedIds: string[],
    failedIds: { id: string; reason: string }[]
  }> {
    this.mediaValidationHelper.isAdmin(isAdmin);
    this.mediaValidationHelper
    .validateMediaArray(
      mediaIds,
      'Danh sách id không được rỗng',
    );
    const deletedIds: string[] = [];
    const skippedIds: string[] = [];
    const failedIds: { id: string; reason: string }[] = [];

    const CHUNK_SIZE = 1000;
    const mediaIdChunks = chunk(mediaIds, CHUNK_SIZE);

    for (const batch of mediaIdChunks) {
      const medias = await this.mediaRepository.find({
        where: { id: In(batch) }
      });

      const mediaMap = new Map(medias.map(m => [m.id, m]));

      for (const mediaId of batch) {
        const media = mediaMap.get(mediaId);
        if (!media) {
          skippedIds.push(mediaId);
          continue;
        }

        try {
          // Cập nhật trạng thái thành DELETED thay vì xóa hoàn toàn
          await this.mediaRepository.update(
            { id: mediaId },
            {
              status: MediaStatusEnum.DELETED,
              updatedAt: Date.now()
            }
          );

          deletedIds.push(mediaId);
        } catch (error) {
          this.log.error(`Lỗi khi xóa mềm media ${mediaId}: ${error.message}`, error.stack);
          failedIds.push({ id: mediaId, reason: error.message });
        }
      }
    }

    return { deletedIds, skippedIds, failedIds };
  }


}
