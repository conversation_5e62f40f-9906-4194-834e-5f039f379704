import { Injectable, Logger } from '@nestjs/common';
import { UserSettingsRepository } from '../repository/user-settings.repository';
import { UpdateUserSettingsDto, UserSettingsResponseDto } from '@modules/user/dto/user-settings.dto';
import { AppException, ErrorCode } from '@/common';

/**
 * Service xử lý logic nghiệp vụ cho user settings
 */
@Injectable()
export class UserSettingsService {
  private readonly logger = new Logger(UserSettingsService.name);

  constructor(
    private readonly userSettingsRepository: UserSettingsRepository,
  ) {}

  /**
   * Lấy user settings theo user ID
   * @param userId ID của người dùng
   * @returns User settings hoặc tạo mới nếu chưa có
   */
  async getUserSettings(userId: number): Promise<UserSettingsResponseDto> {
    let userSettings = await this.userSettingsRepository.findByUserId(userId);

    // Nếu chưa có settings, tạo mới với giá trị mặc định
    if (!userSettings) {
      userSettings = await this.userSettingsRepository.create(userId);
      this.logger.log(`Created default user settings for user ${userId}`);
    }

    return this.mapToResponseDto(userSettings);
  }

  /**
   * Cập nhật user settings
   * @param userId ID của người dùng
   * @param updateUserSettingsDto Dữ liệu cập nhật
   * @returns User settings đã được cập nhật
   */
  async updateUserSettings(
    userId: number,
    updateUserSettingsDto: UpdateUserSettingsDto,
  ): Promise<UserSettingsResponseDto> {
    let userSettings = await this.userSettingsRepository.findByUserId(userId);

    // Nếu chưa có settings, tạo mới
    if (!userSettings) {
      userSettings = await this.userSettingsRepository.create(
        userId,
        updateUserSettingsDto.theme,
        updateUserSettingsDto.timezone,
      );
      this.logger.log(`Created new user settings for user ${userId}`);
    } else {
      // Cập nhật settings hiện có
      const updateData: Partial<{ theme: Record<string, any>; timezone: string }> = {};
      
      if (updateUserSettingsDto.theme !== undefined) {
        updateData.theme = updateUserSettingsDto.theme;
      }
      
      if (updateUserSettingsDto.timezone !== undefined) {
        updateData.timezone = updateUserSettingsDto.timezone;
      }

      if (Object.keys(updateData).length > 0) {
        const updatedSettings = await this.userSettingsRepository.update(userSettings.id, updateData);
        if (!updatedSettings) {
          throw new AppException(
            ErrorCode.RESOURCE_NOT_FOUND,
            `Không thể cập nhật user settings với ID ${userSettings.id}`,
          );
        }
        userSettings = updatedSettings;
        this.logger.log(`Updated user settings for user ${userId}`);
      }
    }

    return this.mapToResponseDto(userSettings);
  }

  /**
   * Xóa user settings
   * @param userId ID của người dùng
   */
  async deleteUserSettings(userId: number): Promise<void> {
    await this.userSettingsRepository.deleteByUserId(userId);
    this.logger.log(`Deleted user settings for user ${userId}`);
  }

  /**
   * Chuyển đổi entity thành response DTO
   * @param userSettings UserSettings entity
   * @returns UserSettingsResponseDto
   */
  private mapToResponseDto(userSettings: any): UserSettingsResponseDto {
    return {
      id: userSettings.id,
      userId: userSettings.userId,
      theme: userSettings.theme,
      timezone: userSettings.timezone,
      createdAt: userSettings.createdAt,
      updatedAt: userSettings.updatedAt,
    };
  }
}
