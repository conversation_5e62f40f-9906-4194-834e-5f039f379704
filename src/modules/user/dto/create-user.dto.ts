import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsEnum, IsOptional, IsString } from 'class-validator';
import { UserTypeEnum, GenderEnum } from '../enums';

export class CreateUserDto {
    @ApiProperty({
        description: 'Tên đầy đủ của người dùng',
        example: 'Nguyễn Văn A',
    })
    @IsString({ message: 'Tên đầy đủ phải là chuỗi' })
    @IsOptional()
    fullName?: string;

    @ApiProperty({
        description: 'Email của người dùng',
        example: '<EMAIL>',
    })
    @IsEmail({}, { message: 'Email không hợp lệ' })
    @IsOptional()
    email?: string;

    @ApiProperty({
        description: 'Mật khẩu của người dùng',
        example: 'password123',
    })
    @IsString({ message: 'Mật khẩu phải là chuỗi' })
    @IsOptional()
    password?: string;

    @ApiProperty({
        description: 'Số điện thoại của người dùng',
        example: '0912345678',
    })
    @IsString({ message: 'Số điện thoại phải là chuỗi' })
    @IsOptional()
    phoneNumber?: string;

    @ApiProperty({
        description: 'Trạng thái tài khoản',
        example: true,
        default: true,
    })
    @IsOptional()
    isActive?: boolean = true;

    @ApiProperty({
        description: 'Trạng thái xác thực email',
        example: false,
        default: false,
    })
    @IsOptional()
    isVerifyEmail?: boolean = false;

    @ApiProperty({
        description: 'Thời gian tạo tài khoản (Unix timestamp)',
        example: 1625097600000,
    })
    @IsOptional()
    createdAt?: number;

    @ApiProperty({
        description: 'Thời gian cập nhật thông tin (Unix timestamp)',
        example: 1625097600000,
    })
    @IsOptional()
    updatedAt?: number;

    @ApiProperty({
        description: 'Loại tài khoản',
        example: UserTypeEnum.INDIVIDUAL,
        enum: UserTypeEnum,
        default: UserTypeEnum.INDIVIDUAL,
    })
    @IsEnum(UserTypeEnum, { message: 'Loại tài khoản không hợp lệ' })
    @IsOptional()
    type?: UserTypeEnum = UserTypeEnum.INDIVIDUAL;

    // Trường role đã được xóa khỏi entity User
    // @ApiProperty({
    //     description: 'Vai trò',
    //     example: 'user',
    //     default: 'user',
    // })
    // @IsString({ message: 'Vai trò phải là chuỗi' })
    // @IsOptional()
    // role?: string = 'user';

    @ApiProperty({
        description: 'Avatar của người dùng',
        example: 'https://example.com/avatar.jpg',
    })
    @IsString({ message: 'Avatar phải là chuỗi' })
    @IsOptional()
    avatar?: string;

    @ApiProperty({
        description: 'Giới tính',
        example: GenderEnum.MALE,
        enum: GenderEnum,
    })
    @IsEnum(GenderEnum, { message: 'Giới tính không hợp lệ' })
    @IsOptional()
    gender?: GenderEnum;

    @ApiProperty({
        description: 'Trạng thái xác thực số điện thoại',
        example: false,
        default: false,
    })
    @IsOptional()
    isVerifyPhone?: boolean;

    @ApiProperty({
        description: 'Mã người giới thiệu',
    })
    @IsOptional()
    affiliateAccountId?: number;

    @ApiProperty({
        description: 'Mã quốc gia của số điện thoại',
        example: '+84',
        default: '+84',
    })
    @IsString({ message: 'Mã quốc gia phải là chuỗi' })
    @IsOptional()
    countryCode?: string;
}