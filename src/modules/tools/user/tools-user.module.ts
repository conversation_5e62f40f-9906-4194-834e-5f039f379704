import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AdminTool,
  UserTool,
  AdminToolVersion,
  UserToolVersion,
  ApiKey,
  OAuth,
  UserToolsCustom
} from '../entities';
import { TypeAgent } from '../../agent/entities/type-agent.entity';
import {
  AdminToolRepository,
  UserToolRepository,
  AdminToolVersionRepository,
  UserToolVersionRepository,
  TypeAgentRepository,
  ApiKeyRepository,
  OAuthRepository,
  UserToolsCustomRepository
} from '../repositories';
import { UserToolService, UserToolVersionService, IntegrationToolsService, UserToolsCustomService } from './services';
import { UserToolController, UserToolVersionController, IntegrationToolsController } from './controllers';

@Module({
  imports: [TypeOrmModule.forFeature([
    UserTool,
    UserToolVersion,
    AdminTool,
    AdminToolVersion,
    TypeAgent,
    ApiKey,
    OAuth,
    UserToolsCustom
  ])],
  controllers: [
    UserToolController,
    UserToolVersionController,
    IntegrationToolsController
  ],
  providers: [
    UserToolService,
    UserToolVersionService,
    IntegrationToolsService,
    UserToolsCustomService,
    UserToolRepository,
    UserToolVersionRepository,
    AdminToolRepository,
    AdminToolVersionRepository,
    TypeAgentRepository,
    ApiKeyRepository,
    OAuthRepository,
    UserToolsCustomRepository
  ],
  exports: [
    UserToolService,
    UserToolVersionService,
    IntegrationToolsService,
    UserToolsCustomService,
    UserToolsCustomRepository,
    ApiKeyRepository,
    OAuthRepository
  ],
})
export class ToolsUserModule {}
