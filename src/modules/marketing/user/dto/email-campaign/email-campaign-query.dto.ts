import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@/common/dto/query.dto';
import { CampaignStatus } from '@/modules/marketing/enums/campaign-status.enum';

/**
 * DTO cho query parameters khi lấy danh sách email campaign có phân trang
 */
export class EmailCampaignQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái campaign
   */
  @ApiProperty({
    description: 'Lọc theo trạng thái campaign',
    enum: CampaignStatus,
    example: CampaignStatus.SENT,
    required: false,
  })
  @IsOptional()
  @IsEnum(CampaignStatus)
  status?: CampaignStatus;

  /**
   * Tìm kiếm theo tiêu đề campaign
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tiêu đề campaign',
    example: 'khuyến mãi',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  constructor() {
    super();
    // Mặc định sắp xếp theo thời gian tạo giảm dần
    this.sortBy = 'createdAt';
    this.sortDirection = SortDirection.DESC;
  }
}
