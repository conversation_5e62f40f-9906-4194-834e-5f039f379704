import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { User } from '@modules/user/entities/user.entity';
import { Agent } from '@modules/agent/entities/agent.entity';
import { UserConvert } from './user-convert.entity';
import { UserOrder } from './user-order.entity';
import { IsOptional } from 'class-validator';

/**
 * Entity đại diện cho bảng user_convert_customers trong cơ sở dữ liệu
 * Khách hàng được chuyển đổi từ nền tảng khác
 */
@Entity('user_convert_customers')
@Unique('user_convert_customers_phone_unique', ['phone'])
export class UserConvertCustomer {
  /**
   * ID khách hàng
   */
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  /**
   * Ảnh đại diện
   */
  @Column({ name: 'avatar', length: 255, nullable: true, comment: 'Ảnh đại diện' })
  avatar: string;

  /**
   * Tên khách hàng
   */
  @Column({ name: 'name', length: 255, nullable: true, comment: 'Tên khách hàng' })
  name: string;

  /**
   * Email khách hàng (dạng JSON)
   */
  @Column({ name: 'email', type: 'jsonb', nullable: true, comment: 'Email khách hàng (dạng JSON)' })
  email: Record<string, string>;

  /**
   * Số điện thoại khách hàng
   */
  @Column({ name: 'phone', length: 20, nullable: true, comment: 'Số điện thoại khách hàng' })
  phone: string;

  /**
   * Nền tảng nguồn (Facebook, Web,...)
   */
  @Column({ name: 'platform', length: 50, nullable: true, comment: 'Nền tảng nguồn (Facebook, Web,...)' })
  platform: string;

  /**
   * Múi giờ của khách hàng
   */
  @Column({ name: 'timezone', length: 50, nullable: true, comment: 'Múi giờ của khách hàng' })
  timezone: string;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: false, default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint", comment: 'Thời gian tạo' })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: false, default: () => "(EXTRACT(epoch FROM now()) * 1000)::bigint", comment: 'Thời gian cập nhật' })
  updatedAt: number;

  /**
   * Người dùng sở hữu khách hàng
   */
  @Column({ name: 'user_id', type: 'bigint', nullable: true, comment: 'Người dùng sở hữu khách hàng' })
  userId: number;

  /**
   * ID agent hỗ trợ khách hàng
   * Tham chiếu đến bảng agents
   */
  @Column({ name: 'agent_id', type: 'uuid', nullable: true, comment: 'ID agent hỗ trợ khách hàng' })
  agentId: string;

  /**
   * Trường tùy chỉnh
   */
  @Column({ name: 'metadata', type: 'jsonb', nullable: false, default: () => "'[]'::jsonb", comment: 'Trường tùy chỉnh' })
  metadata: Record<string, unknown>;

  /**
   * Link Facebook của khách hàng
   */
  @Column({ name: 'facebook_link', length: 500, nullable: true, comment: 'Link Facebook của khách hàng' })
  facebookLink: string;

  /**
   * Link Twitter của khách hàng
   */
  @Column({ name: 'twitter_link', length: 500, nullable: true, comment: 'Link Twitter của khách hàng' })
  twitterLink: string;

  /**
   * Link LinkedIn của khách hàng
   */
  @Column({ name: 'linkedin_link', length: 500, nullable: true, comment: 'Link LinkedIn của khách hàng' })
  linkedinLink: string;

  /**
   * Link Zalo của khách hàng
   */
  @Column({ name: 'zalo_link', length: 500, nullable: true, comment: 'Link Zalo của khách hàng' })
  zaloLink: string;

  /**
   * Link Website của khách hàng
   */
  @Column({ name: 'website_link', length: 500, nullable: true, comment: 'Link Website của khách hàng' })
  websiteLink: string;
}
