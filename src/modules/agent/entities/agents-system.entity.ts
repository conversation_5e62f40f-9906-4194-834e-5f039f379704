import { Column, CreateDateColumn, DeleteDateColumn, Entity, PrimaryColumn, Unique, UpdateDateColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agents_system trong cơ sở dữ liệu
 * Bảng lưu thông tin agent hệ thống, ví dụ: agent tự động hoặc mặc định của hệ thống
 */
@Entity('agents_system')
@Unique(['nameCode'])
export class AgentSystem {
  /**
   * UUID tham chiếu từ agents.id
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * UUID của vai trò liên kết
   * Mỗi agent system chỉ có 1 role duy nhất
   */
  @PrimaryColumn({ name: 'role_id', type: 'uuid' })
  roleId: string;

  /**
   * Mã định danh của agent system, dùng để định danh trong code
   */
  @Column({
    name: 'name_code',
    type: 'varchar',
    length: 100,
    nullable: false,
    unique: true,
  })
  nameCode: string;

  /**
   * ID nhân viên tạo
   */
  @CreateDateColumn({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID nhân viên cập nhật
   */
  @UpdateDateColumn({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * ID nhân viên xóa
   */
  @DeleteDateColumn({ name: 'deleted_by', type: 'integer', nullable: true })
  deletedBy: number | null;

  /**
   * Prompt cho task
   */
  @Column({ name: 'prompt_task', type: 'text', nullable: true })
  promptTask: string;

  /**
 * UUID tham chiếu đến bảng base_models
 * ID của model base (nếu có)
 */
  @Column({ name: 'model_base_id', type: 'uuid', nullable: true })
  modelBaseId: string | null;

  /**
   * UUID tham chiếu đến bảng fine_tuning_models
   * ID của fine-tuning model (nếu có)
   */
  @Column({ name: 'model_finetuning_id', type: 'uuid', nullable: true })
  modelFinetuningId: string | null;
}
