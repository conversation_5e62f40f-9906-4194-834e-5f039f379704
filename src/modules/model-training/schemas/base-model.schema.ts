import { ApiProperty } from '@nestjs/swagger';
import { BaseModel } from '../entities/base-model.entity';

export class BaseModelSchema {
  @ApiProperty({
    description: 'ID định danh duy nhất cho mô hình',
    example: 'gpt-4-turbo',
  })
  id: string;

  @ApiProperty({
    description: 'Tên hiển thị của mô hình nền tảng',
    example: 'GPT-4 Turbo',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về mô hình và khả năng của nó',
    example: 'Phiên bản mới nhất của GPT-4 với khả năng xử lý văn bản nhanh hơn và hiệu quả hơn',
    nullable: true,
  })
  description: string;

  @ApiProperty({
    description: 'ID nhà cung cấp mô hình (UUID)',
    example: '123e4567-e89b-12d3-a456-************',
    nullable: true,
  })
  providerId: string;

  @ApiProperty({
    description: 'Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình cơ bản',
    example: 10,
  })
  baseInputRate: number;

  @ApiProperty({
    description: 'Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình cơ bản',
    example: 30,
  })
  baseOutputRate: number;

  @ApiProperty({
    description: 'Tỷ lệ tính phí cho huấn luyện với mô hình cơ bản',
    example: 80,
  })
  baseTrainRate: number;

  @ApiProperty({
    description: 'Tỷ lệ tính phí cho đầu vào khi sử dụng mô hình fine-tuning',
    example: 12,
  })
  fineTuningInputRate: number;

  @ApiProperty({
    description: 'Tỷ lệ tính phí cho đầu ra khi sử dụng mô hình fine-tuning',
    example: 36,
  })
  fineTuningOutputRate: number;

  @ApiProperty({
    description: 'Tỷ lệ tính phí cho huấn luyện với mô hình fine-tuning',
    example: 100,
  })
  fineTuningTrainRate: number;

  @ApiProperty({
    description: 'Số token tối đa mô hình có thể xử lý trong một lần request',
    example: 128000,
  })
  tokenCount: number;



  @ApiProperty({
    description: 'ID nhân viên tạo bản ghi mô hình',
    example: 1,
    nullable: true,
  })
  createdBy: number;

  @ApiProperty({
    description: 'ID nhân viên cập nhật bản ghi mô hình gần nhất',
    example: 1,
    nullable: true,
  })
  updatedBy: number;

  @ApiProperty({
    description: 'Thời điểm tạo bản ghi (timestamp millis)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật bản ghi gần nhất (timestamp millis)',
    example: 1625097600000,
  })
  updatedAt: number;

  constructor(partial: Partial<BaseModel>) {
    Object.assign(this, partial);
  }
}

export class BaseModelListResponseSchema {
  @ApiProperty({
    description: 'Danh sách mô hình nền tảng',
    type: [BaseModelSchema],
  })
  items: BaseModelSchema[];

  @ApiProperty({
    description: 'Thông tin phân trang',
    type: 'object',
    properties: {
      totalItems: {
        type: 'number',
        example: 100,
        description: 'Tổng số mô hình',
      },
      itemCount: {
        type: 'number',
        example: 10,
        description: 'Số mô hình trên trang hiện tại',
      },
      itemsPerPage: {
        type: 'number',
        example: 10,
        description: 'Số mô hình trên mỗi trang',
      },
      totalPages: {
        type: 'number',
        example: 10,
        description: 'Tổng số trang',
      },
      currentPage: {
        type: 'number',
        example: 1,
        description: 'Trang hiện tại',
      },
    },
  })
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
