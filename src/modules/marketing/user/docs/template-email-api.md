# Template Email API Documentation

## Tổng quan

API Template Email cho phép người dùng tạo, quản lý và sử dụng các mẫu email với tính năng nâng cao bao gồm:

- Tự động extract placeholders từ nội dung HTML
- Quản lý biến động với metadata chi tiết
- Hỗ trợ nhiều loại template (Newsletter, Promotional, Transactional, etc.)
- Validation dữ liệu toàn diện
- Preview text cho email clients

## Endpoint

```
POST /api/v1/marketing/template-emails
```

## Authentication

- **Required**: JWT Bearer Token
- **Guard**: JwtUserGuard

## Request Body

### Trường bắt buộc

| Field | Type | Description |
|-------|------|-------------|
| `name` | string | Tên template (1-255 ký tự, unique trong scope user) |
| `subject` | string | Tiêu đề email (1-255 ký tự) |
| `htmlContent` | string | Nội dung HTML của email |

### Trường tùy chọn

| Field | Type | Description | Default |
|-------|------|-------------|---------|
| `textContent` | string | Nội dung text thuần | null |
| `type` | enum | Loại template | 'NEWSLETTER' |
| `previewText` | string | Preview text (max 255 ký tự) | null |
| `tags` | string[] | Danh sách tags | [] |
| `variables` | EmailVariable[] | Định nghĩa biến | [] |

### EmailVariable Object

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `name` | string | ✅ | Tên biến (a-z, A-Z, 0-9, _) |
| `type` | enum | ✅ | Loại dữ liệu (TEXT, NUMBER, DATE, URL, IMAGE) |
| `defaultValue` | string | ❌ | Giá trị mặc định |
| `required` | boolean | ❌ | Biến có bắt buộc không (default: false) |
| `description` | string | ❌ | Mô tả biến (max 500 ký tự) |

## Response Format

### Success Response (201)

```json
{
  "code": 201,
  "message": "Tạo template email thành công",
  "result": {
    "id": 1,
    "userId": 1,
    "name": "Newsletter tháng 1",
    "subject": "🎉 Khuyến mãi đặc biệt dành cho bạn!",
    "htmlContent": "<!DOCTYPE html>...",
    "textContent": "Xin chào {customer_name}!...",
    "type": "NEWSLETTER",
    "status": "DRAFT",
    "previewText": "Đừng bỏ lỡ ưu đãi...",
    "tags": ["newsletter", "promotion"],
    "placeholders": ["customer_name", "product_name"],
    "variableMetadata": {
      "customer_name": {
        "type": "TEXT",
        "defaultValue": "Khách hàng",
        "required": true,
        "description": "Tên khách hàng"
      }
    },
    "createdAt": 1640995200000,
    "updatedAt": 1640995200000
  }
}
```

### Error Responses

#### 400 Bad Request
```json
{
  "code": 400,
  "message": "Dữ liệu không hợp lệ",
  "errors": [
    {
      "field": "name",
      "message": "Tên template là bắt buộc"
    }
  ]
}
```

#### 409 Conflict
```json
{
  "code": 409,
  "message": "Template với tên này đã tồn tại"
}
```

## Tính năng đặc biệt

### 1. Auto-extract Placeholders

Hệ thống tự động extract placeholders từ htmlContent HTML:

```html
<!-- Input -->
<p>Xin chào {customer_name}! Giảm giá {discount}%</p>

<!-- Output placeholders -->
["customer_name", "discount"]
```

### 2. Variable Metadata

Cho phép định nghĩa chi tiết cho từng biến:

```json
{
  "name": "customer_name",
  "type": "TEXT",
  "defaultValue": "Khách hàng",
  "required": true,
  "description": "Tên khách hàng hiển thị trong email"
}
```

### 3. Tags Processing

- Tự động trim whitespace
- Loại bỏ tags rỗng
- Không duplicate

### 4. Validation Rules

- **Template name**: Unique trong scope user
- **Variable names**: Unique trong template
- **HTML content**: Kiểm tra cơ bản
- **Variable name format**: Regex `/^[a-zA-Z_][a-zA-Z0-9_]*$/`

## Ví dụ sử dụng

### Ví dụ 1: Template cơ bản

```bash
curl -X POST /api/v1/marketing/template-emails \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Newsletter tháng 1",
    "subject": "Khuyến mãi đặc biệt",
    "htmlContent": "<h1>Xin chào {customer_name}!</h1>"
  }'
```

### Ví dụ 2: Template với biến phức tạp

```bash
curl -X POST /api/v1/marketing/template-emails \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Welcome Email",
    "subject": "Chào mừng {user_name}!",
    "htmlContent": "<div><h2>Chào mừng {user_name}!</h2><p>Mã xác thực: {code}</p></div>",
    "type": "WELCOME",
    "tags": ["welcome", "verification"],
    "variables": [
      {
        "name": "user_name",
        "type": "TEXT",
        "defaultValue": "Người dùng",
        "required": true,
        "description": "Tên người dùng"
      },
      {
        "name": "code",
        "type": "TEXT",
        "required": true,
        "description": "Mã xác thực"
      }
    ]
  }'
```

## Migration Database

Trước khi sử dụng API, cần chạy migration theo thứ tự:

### Cách 1: Chạy migration đơn giản (Khuyến nghị)
```sql
-- Bước 1: Thêm các cột mới
\i src/modules/marketing/user/migrations/add-new-fields-to-user-template-email-simple.sql

-- Bước 2: Thêm constraints (tùy chọn)
\i src/modules/marketing/user/migrations/add-constraints-to-user-template-email.sql
```

### Cách 2: Chạy migration đầy đủ
```sql
-- Chạy file migration đầy đủ (có thể gặp lỗi syntax trên một số phiên bản PostgreSQL)
\i src/modules/marketing/user/migrations/add-new-fields-to-user-template-email.sql
```

### Kiểm tra migration thành công
```sql
-- Kiểm tra các cột đã được thêm
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'user_template_email'
ORDER BY ordinal_position;

-- Kiểm tra indexes
SELECT indexname, indexdef
FROM pg_indexes
WHERE tablename = 'user_template_email';
```

## Testing

Sử dụng test examples:

```typescript
import { createTemplateEmailExamples } from './test-examples/create-template-email.examples';

// Test với ví dụ cơ bản
const basicExample = createTemplateEmailExamples.basicNewsletter;
```

## Lưu ý quan trọng

1. **Unique constraint**: Tên template phải unique trong scope của user
2. **Placeholder sync**: Placeholders được tự động sync từ content và variables
3. **Default values**: Type mặc định là 'NEWSLETTER', status mặc định là 'DRAFT'
4. **Performance**: Có index trên các trường thường query (type, status, user_id)
5. **Validation**: Tất cả dữ liệu đầu vào được validate nghiêm ngặt
