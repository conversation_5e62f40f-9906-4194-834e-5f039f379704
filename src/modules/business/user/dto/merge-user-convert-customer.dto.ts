import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsObject, Matches, MaxLength, MinLength, IsNumber, IsUUID } from 'class-validator';

/**
 * DTO cho việc merge khách hàng chuyển đổi
 * Merge 2 customer hiện có thành 1, customer nguồn sẽ bị xóa, customer đích sẽ được cập nhật
 */
export class MergeUserConvertCustomerDto {
  /**
   * ID khách hàng nguồn (sẽ bị xóa sau khi merge)
   * @example 1
   */
  @ApiProperty({
    description: 'ID khách hàng nguồn (sẽ bị xóa sau khi merge)',
    example: 1,
  })
  @IsNotEmpty({ message: 'ID khách hàng nguồn không được để trống' })
  @IsNumber({}, { message: 'ID khách hàng nguồn phải là số' })
  sourceCustomerId: number;

  /**
   * ID khách hàng đích (sẽ được cập nhật với dữ liệu merge)
   * @example 2
   */
  @ApiProperty({
    description: 'ID khách hàng đích (sẽ được cập nhật với dữ liệu merge)',
    example: 2,
  })
  @IsNotEmpty({ message: 'ID khách hàng đích không được để trống' })
  @IsNumber({}, { message: 'ID khách hàng đích phải là số' })
  targetCustomerId: number;

  /**
   * Tên khách hàng sau merge
   * @example "Nguyễn Văn A"
   */
  @ApiProperty({
    description: 'Tên khách hàng sau merge',
    example: 'Nguyễn Văn A',
  })
  @IsNotEmpty({ message: 'Tên khách hàng không được để trống' })
  @IsString({ message: 'Tên khách hàng phải là chuỗi' })
  @MinLength(2, { message: 'Tên khách hàng phải có ít nhất 2 ký tự' })
  @MaxLength(255, { message: 'Tên khách hàng không được vượt quá 255 ký tự' })
  name: string;

  /**
   * Số điện thoại khách hàng sau merge (unique)
   * @example "0912345678"
   */
  @ApiProperty({
    description: 'Số điện thoại khách hàng sau merge (unique)',
    example: '0912345678',
  })
  @IsNotEmpty({ message: 'Số điện thoại không được để trống' })
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @Matches(/^(0|\+84)[3|5|7|8|9][0-9]{8}$/, {
    message: 'Số điện thoại không hợp lệ, phải là số điện thoại Việt Nam',
  })
  phone: string;

  /**
   * Email khách hàng sau merge (dạng JSON)
   * @example { "primary": "<EMAIL>", "secondary": "<EMAIL>" }
   */
  @ApiProperty({
    description: 'Email khách hàng sau merge (dạng JSON)',
    example: { primary: '<EMAIL>', secondary: '<EMAIL>' },
    required: false,
  })
  @IsOptional()
  @IsObject({ message: 'Email phải là đối tượng JSON' })
  email?: Record<string, string>;

  /**
   * Tag/nhãn cho khách hàng sau merge
   * @example ["VIP", "Potential", "Hot Lead"]
   */
  @ApiProperty({
    description: 'Tag/nhãn cho khách hàng sau merge',
    example: ['VIP', 'Potential', 'Hot Lead'],
    required: false,
    type: [String],
  })
  @IsOptional()
  tags?: string[];



  /**
   * Nền tảng nguồn sau merge (Facebook, Web,...)
   * @example "Facebook"
   */
  @ApiProperty({
    description: 'Nền tảng nguồn sau merge (Facebook, Web,...)',
    example: 'Facebook',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Nền tảng nguồn phải là chuỗi' })
  @MaxLength(50, { message: 'Nền tảng nguồn không được vượt quá 50 ký tự' })
  platform?: string;

  /**
   * Múi giờ của khách hàng sau merge
   * @example "Asia/Ho_Chi_Minh"
   */
  @ApiProperty({
    description: 'Múi giờ của khách hàng sau merge',
    example: 'Asia/Ho_Chi_Minh',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Múi giờ phải là chuỗi' })
  @MaxLength(50, { message: 'Múi giờ không được vượt quá 50 ký tự' })
  timezone?: string;

  /**
   * ID agent hỗ trợ khách hàng sau merge
   * @example "550e8400-e29b-41d4-a716-446655440000"
   */
  @ApiProperty({
    description: 'ID agent hỗ trợ khách hàng sau merge',
    example: '550e8400-e29b-41d4-a716-446655440000',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'ID agent phải là chuỗi' })
  agentId?: string;
}
