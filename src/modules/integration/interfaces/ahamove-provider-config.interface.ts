/**
 * Interface cho Ahamove
 * Yêu cầu: API Key, Mobile và Token
 */
export interface AhamoveProviderConfig {
  /** API Key từ partner registration */
  apiKey: string;
  /** Số điện thoại tài khoản */
  mobile: string;
  /** JWT <PERSON> (có thể được refresh) */
  token?: string;
  /** Refresh Token */
  refreshToken?: string;
  /** Thời gian hết hạn token */
  tokenExpiry?: number;
  /** Môi trường (staging/production) */
  environment?: 'staging' | 'production';
  /** Tên hiển thị của cấu hình */
  name?: string;
  /** Trạng thái kích hoạt */
  isActive?: boolean;
  /** Ghi chú */
  notes?: string;
}
