import { GenderEnum, UserTypeEnum } from '@/modules/user/enums';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
} from 'typeorm';

@Entity({ name: 'users' })
export class UserEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'full_name', type: 'varchar', length: 100, nullable: true })
  fullName: string;

  @Column({ type: 'varchar', length: 100, unique: true, nullable: true })
  email: string;

  @Column({
    name: 'phone_number',
    type: 'varchar',
    length: 45,
    unique: true,
    nullable: true,
  })
  phoneNumber: string;

  @Column({ name: 'is_active', type: 'boolean', default: true })
  isActive: boolean;

  @Column({ name: 'is_verify_email', type: 'boolean', default: false })
  isVerifyEmail: boolean;

  @Column({ name: 'is_verify_phone', type: 'boolean', default: false })
  isVerifyPhone: boolean;

  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number; // Consider using CreateDateColumn for automatic handling

  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number; // Consider using UpdateDateColumn for automatic handling

  @Column({
    name: 'citizen_issue_place',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  citizenIssuePlace: string;

  @Column({ name: 'citizen_issue_date', type: 'date', nullable: true })
  citizenIssueDate: Date;

  @Column({ name: 'is_first_password_change', type: 'boolean', default: false })
  isFirstPasswordChange: boolean;

  @Column({ type: 'integer', nullable: true })
  country: number;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  address: string;

  @Column({ name: 'tax_code', type: 'varchar', length: 20, nullable: true })
  taxCode: string;

  @Column({ name: 'points_balance', type: 'bigint', default: 0 })
  pointsBalance: number;

  @Column({
    type: 'enum',
    enum: UserTypeEnum,
    default: UserTypeEnum.INDIVIDUAL,
  })
  type: UserTypeEnum;

  @Column({ type: 'varchar', length: 20, nullable: true })
  platform: string;

  @Column({ name: 'citizen_id', type: 'varchar', length: 20, nullable: true })
  citizenId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  avatar: string;

  @Column({ type: 'varchar', length: 1000, nullable: true })
  password?: string; // Marked as optional as it might not always be selected

  @Column({ name: 'date_of_birth', type: 'date', nullable: true })
  dateOfBirth: Date;

  @Column({
    type: 'enum',
    enum: GenderEnum,
    nullable: true,
  })
  gender: GenderEnum;

  @Column({ name: 'bank_code', type: 'varchar', length: 20, nullable: true })
  bankCode: string;

  @Column({
    name: 'account_number',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  accountNumber: string;

  @Column({
    name: 'account_holder',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  accountHolder: string;

  @Column({ name: 'bank_branch', type: 'varchar', length: 255, nullable: true })
  bankBranch: string;

  @Column({ type: 'varchar', length: 20 })
  role: string;

  @Column({ name: 'alert_threshold', type: 'bigint' })
  alertThreshold: number; // mức rpoint cảnh báo

  @Column({ name: 'was_rpoint_alerted', type: 'boolean' })
  wasRpointAlerted: number; // đã cảnh báo rpoint hay chưa

  @Column({ name: 'country_code', type: 'varchar', length: 10, default: '+84', nullable: true })
  countryCode: string;
}