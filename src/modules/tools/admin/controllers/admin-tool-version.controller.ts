import { ApiResponseDto } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@common/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import {
  CreateToolVersionDto,
  UpdateToolVersionDto,
  VersionDto,
} from '../dto';
import { AdminToolVersionService } from '../services';

@ApiTags(SWAGGER_API_TAGS.ADMIN_VERSION_TOOL)
@Controller('admin/tools/:toolId/versions')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminToolVersionController {
  constructor(private readonly adminToolVersionService: AdminToolVersionService) { }

  @Post()
  @ApiOperation({ summary: 'Tạo mới phiên bản tool' })
  @ApiParam({ name: 'toolId', description: 'ID của tool' })
  @ApiResponse({
    status: 201,
    description: 'Phiên bản đã được tạo thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
    TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID
  )
  async createVersion(
    @Param('toolId') toolId: string,
    @CurrentEmployee('id') employeeId: number,
    @Body() createDto: CreateToolVersionDto,
  ) {
    const versionId = await this.adminToolVersionService.createVersion(
      toolId,
      employeeId,
      createDto,
    );
    return ApiResponseDto.success({ id: versionId });
  }

  @Get(':versionId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết phiên bản' })
  @ApiParam({ name: 'toolId', description: 'ID của tool' })
  @ApiParam({ name: 'versionId', description: 'ID của phiên bản' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết phiên bản',
    type: () => ApiResponseDto.success(VersionDto),
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND
  )
  async getVersionById(
    @Param('versionId') versionId: string,
  ) {
    const version = await this.adminToolVersionService.getVersionById(versionId);
    return ApiResponseDto.success(version);
  }

  @Put(':versionId')
  @ApiOperation({ summary: 'Cập nhật thông tin phiên bản' })
  @ApiParam({ name: 'toolId', description: 'ID của tool' })
  @ApiParam({ name: 'versionId', description: 'ID của phiên bản' })
  @ApiResponse({
    status: 200,
    description: 'Phiên bản đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
    TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID
  )
  async updateVersion(
    @Param('versionId') versionId: string,
    @CurrentEmployee('id') employeeId: number,
    @Body() updateDto: UpdateToolVersionDto,
  ) {
    const updatedVersionId = await this.adminToolVersionService.updateVersion(
      versionId,
      employeeId,
      updateDto,
    );
    return ApiResponseDto.success({ id: updatedVersionId });
  }

  @Post(':versionId/set-default')
  @ApiOperation({ summary: 'Đặt phiên bản làm mặc định' })
  @ApiParam({ name: 'toolId', description: 'ID của tool' })
  @ApiParam({ name: 'versionId', description: 'ID của phiên bản' })
  @ApiResponse({
    status: 200,
    description: 'Phiên bản đã được đặt làm mặc định',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_VERSION_INVALID
  )
  async setDefaultVersion(
    @Param('toolId') functionId: string,
    @Param('versionId') versionId: string,
    @CurrentEmployee('id') employeeId: number,
  ) {
    await this.adminToolVersionService.setDefaultVersion(
      functionId,
      versionId,
      employeeId,
    );
    return ApiResponseDto.success({ message: 'Phiên bản đã được đặt làm mặc định' });
  }

  @Delete(':versionId')
  @ApiOperation({ summary: 'Gỡ phiên bản (xóa mềm)' })
  @ApiParam({ name: 'toolId', description: 'ID của tool' })
  @ApiParam({ name: 'versionId', description: 'ID của phiên bản' })
  @ApiResponse({
    status: 200,
    description: 'Phiên bản đã được gỡ thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND
  )
  async removeVersion(
    @Param('versionId') versionId: string,
    @CurrentEmployee('id') employeeId: number,
  ) {
    await this.adminToolVersionService.removeVersion(versionId, employeeId);
    return ApiResponseDto.success({ message: 'Phiên bản đã được gỡ thành công' });
  }
}
