import { CreateUserProviderShipmentsTable1734000000000 } from './1734000000000-CreateUserProviderShipmentsTable';

describe('CreateUserProviderShipmentsTable Migration', () => {
  let migration: CreateUserProviderShipmentsTable1734000000000;
  let mockQueryRunner: any;

  beforeEach(() => {
    migration = new CreateUserProviderShipmentsTable1734000000000();
    mockQueryRunner = {
      query: jest.fn(),
      createTable: jest.fn(),
      dropTable: jest.fn(),
    };
  });

  describe('up migration', () => {
    it('should create provider_shipment_type enum', async () => {
      await migration.up(mockQueryRunner);

      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        expect.stringContaining('CREATE TYPE "provider_shipment_type" AS ENUM')
      );
      expect(mockQueryRunner.query).toHaveBeenCalledWith(
        expect.stringContaining("'GHN', 'GHTK', 'AHAMOVE', 'JT'")
      );
    });

    it('should create user_provider_shipments table with correct structure', async () => {
      await migration.up(mockQueryRunner);

      expect(mockQueryRunner.createTable).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'user_provider_shipments',
          columns: expect.arrayContaining([
            expect.objectContaining({
              name: 'id',
              type: 'uuid',
              isPrimary: true,
              generationStrategy: 'uuid',
            }),
            expect.objectContaining({
              name: 'user_id',
              type: 'int',
              isNullable: false,
            }),
            expect.objectContaining({
              name: 'name',
              type: 'varchar',
              length: '255',
              isNullable: true,
            }),
            expect.objectContaining({
              name: 'key',
              type: 'text',
              isNullable: false,
            }),
            expect.objectContaining({
              name: 'type',
              type: 'enum',
              enum: ['GHN', 'GHTK', 'AHAMOVE', 'JT'],
              isNullable: false,
            }),
            expect.objectContaining({
              name: 'created_at',
              type: 'bigint',
            }),
          ]),
        }),
        true
      );
    });

    it('should create foreign key constraint to users table', async () => {
      await migration.up(mockQueryRunner);

      expect(mockQueryRunner.createTable).toHaveBeenCalledWith(
        expect.objectContaining({
          foreignKeys: expect.arrayContaining([
            expect.objectContaining({
              columnNames: ['user_id'],
              referencedTableName: 'users',
              referencedColumnNames: ['id'],
              onDelete: 'CASCADE',
            }),
          ]),
        }),
        true
      );
    });

    it('should create proper indexes', async () => {
      await migration.up(mockQueryRunner);

      expect(mockQueryRunner.createTable).toHaveBeenCalledWith(
        expect.objectContaining({
          indices: expect.arrayContaining([
            expect.objectContaining({
              name: 'idx_user_provider_shipments_user_id',
              columnNames: ['user_id'],
            }),
            expect.objectContaining({
              name: 'idx_user_provider_shipments_type',
              columnNames: ['type'],
            }),
            expect.objectContaining({
              name: 'idx_user_provider_shipments_user_type',
              columnNames: ['user_id', 'type'],
            }),
            expect.objectContaining({
              name: 'idx_user_provider_shipments_created_at',
              columnNames: ['created_at'],
            }),
          ]),
        }),
        true
      );
    });
  });

  describe('down migration', () => {
    it('should drop table and enum in correct order', async () => {
      await migration.down(mockQueryRunner);

      expect(mockQueryRunner.dropTable).toHaveBeenCalledWith('user_provider_shipments');
      expect(mockQueryRunner.query).toHaveBeenCalledWith('DROP TYPE "provider_shipment_type"');
    });
  });

  describe('migration name', () => {
    it('should have correct migration name', () => {
      expect(migration.name).toBe('CreateUserProviderShipmentsTable1734000000000');
    });
  });
});
