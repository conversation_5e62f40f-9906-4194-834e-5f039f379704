import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { BaseModelRepository } from '@modules/model-training/repositories/base-model.repository';
import { FineTuningModelRepository } from '@modules/model-training/repositories/fine-tuning-model.repository';
import { UserProviderModelRepository } from '@modules/model-training/repositories/user-provider-model.repository';
import { AiProviderHelper } from '@shared/services/ai/helpers/ai-provider.helper';

/**
 * Helper class để validation model configuration cho agent
 */
@Injectable()
export class ModelValidationHelper {
  private readonly logger = new Logger(ModelValidationHelper.name);

  constructor(
    private readonly baseModelRepository: BaseModelRepository,
    private readonly fineTuningModelRepository: FineTuningModelRepository,
    private readonly userProviderModelRepository: UserProviderModelRepository,
    private readonly aiProviderHelper: AiProviderHelper,
  ) {}

  /**
   * Validate model configuration cho agent creation
   * @param userId ID của user
   * @param model_base_id ID của base model (optional)
   * @param model_finetuning_id ID của finetuning model (optional)
   * @param model_id ID của model từ provider (optional)
   * @param provider_id ID của provider (optional)
   */
  async validateModelConfig(
    userId: number,
    model_base_id?: string,
    model_finetuning_id?: string,
    model_id?: string,
    provider_id?: string,
  ): Promise<void> {
    // Kiểm tra ít nhất một option được cung cấp
    const hasBaseModel = !!model_base_id;
    const hasFinetuningModel = !!model_finetuning_id;
    const hasPersonalModel = !!(model_id && provider_id);

    if (!hasBaseModel && !hasFinetuningModel && !hasPersonalModel) {
      throw new AppException(
        AGENT_ERROR_CODES.MODEL_NOT_CONFIGURED,
        'Phải cung cấp ít nhất một trong các options: model_base_id, model_finetuning_id, hoặc cả model_id và provider_id'
      );
    }

    // Validate base model nếu có
    if (hasBaseModel) {
      await this.validateBaseModel(model_base_id);
    }

    // Validate finetuning model nếu có
    if (hasFinetuningModel) {
      await this.validateFineTuningModel(model_finetuning_id);
    }

    // Validate personal model nếu có
    if (hasPersonalModel) {
      await this.validatePersonalModel(userId, model_id, provider_id);
    }
  }

  /**
   * Validate base model tồn tại trong database
   * @param model_base_id ID của base model
   */
  private async validateBaseModel(model_base_id: string): Promise<void> {
    try {
      const exists = await this.baseModelRepository.existsBy({ id: model_base_id });
      if (!exists) {
        throw new AppException(
          AGENT_ERROR_CODES.BASE_MODEL_NOT_FOUND,
          `Base model với ID ${model_base_id} không tồn tại`
        );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi validate base model: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.MODEL_VALIDATION_FAILED,
        `Lỗi khi validate base model: ${error.message}`
      );
    }
  }

  /**
   * Validate finetuning model tồn tại trong database
   * @param model_finetuning_id ID của finetuning model
   */
  private async validateFineTuningModel(model_finetuning_id: string): Promise<void> {
    try {
      const exists = await this.fineTuningModelRepository.existsBy({ id: model_finetuning_id });
      if (!exists) {
        throw new AppException(
          AGENT_ERROR_CODES.FINETUNING_MODEL_NOT_FOUND,
          `Fine-tuning model với ID ${model_finetuning_id} không tồn tại`
        );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi validate finetuning model: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.MODEL_VALIDATION_FAILED,
        `Lỗi khi validate finetuning model: ${error.message}`
      );
    }
  }

  /**
   * Validate personal model với provider thông qua AiProviderHelper
   * @param userId ID của user
   * @param model_id ID của model
   * @param provider_id ID của provider
   */
  private async validatePersonalModel(
    userId: number,
    model_id: string,
    provider_id: string,
  ): Promise<void> {
    try {
      // Lấy thông tin user provider model
      const userProviderModel = await this.userProviderModelRepository.findOne({ where: { id: provider_id } });
      if (!userProviderModel) {
        throw new AppException(
          AGENT_ERROR_CODES.USER_PROVIDER_MODEL_NOT_FOUND,
          `User provider model với ID ${provider_id} không tồn tại`
        );
      }

      // Kiểm tra user có quyền sử dụng provider này không
      if (userProviderModel.userId !== userId) {
        throw new AppException(
          AGENT_ERROR_CODES.USER_PROVIDER_MODEL_ACCESS_DENIED,
          `User không có quyền sử dụng provider model với ID ${provider_id}`
        );
      }

      // Sử dụng AiProviderHelper để validate model với provider thực tế
      await this.aiProviderHelper.retrieveModel(
        model_id,
        userProviderModel.type,
        userProviderModel.apiKey,
        false, // isAdmin = false vì đây là user
        userId
      );

    } catch (error) {
      this.logger.error(`Lỗi khi validate personal model: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        AGENT_ERROR_CODES.MODEL_VALIDATION_FAILED,
        `Lỗi khi validate personal model: ${error.message}`
      );
    }
  }
}
