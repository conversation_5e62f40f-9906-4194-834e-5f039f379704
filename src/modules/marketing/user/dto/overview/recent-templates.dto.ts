import { ApiProperty } from '@nestjs/swagger';

/**
 * Enum cho trạng thái template
 */
export enum TemplateStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
}

/**
 * DTO cho template email gần đây
 */
export class RecentTemplateDto {
  /**
   * ID của template
   * @example 1
   */
  @ApiProperty({
    description: 'ID của template',
    example: 1,
  })
  id: number;

  /**
   * Tên email
   * @example "Welcome Email Template"
   */
  @ApiProperty({
    description: 'Tên email',
    example: 'Welcome Email Template',
  })
  name: string;

  /**
   * Tiêu đề email
   * @example "Chào mừng bạn đến với dịch vụ của chúng tôi"
   */
  @ApiProperty({
    description: 'Tiêu đề email',
    example: 'Chào mừng bạn đến với dịch vụ của chúng tôi',
  })
  subject: string;

  /**
   * Trạng thái template
   * @example "ACTIVE"
   */
  @ApiProperty({
    description: 'Trạng thái template',
    enum: TemplateStatus,
    example: TemplateStatus.ACTIVE,
  })
  status: TemplateStatus;

  /**
   * Ngày tạo (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Ngày tạo (Unix timestamp)',
    example: 1619171200,
  })
  createdAt: number;
}

/**
 * DTO cho phản hồi danh sách templates gần đây
 */
export class RecentTemplatesResponseDto {
  /**
   * Danh sách 5 templates gần nhất
   */
  @ApiProperty({
    description: 'Danh sách 5 templates gần nhất',
    type: [RecentTemplateDto],
  })
  templates: RecentTemplateDto[];
}
