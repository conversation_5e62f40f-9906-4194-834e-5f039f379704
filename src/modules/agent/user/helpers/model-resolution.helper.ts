import { Injectable, Logger } from '@nestjs/common';
import { BaseModelRepository } from '@modules/model-training/repositories/base-model.repository';
import { FineTuningModelRepository } from '@modules/model-training/repositories/fine-tuning-model.repository';
import { UserProviderModelRepository } from '@modules/model-training/repositories/user-provider-model.repository';

/**
 * Interface cho thông tin model đã resolve
 */
export interface ResolvedModelInfo {
  model_id: string;
  provider?: string;
  model_base_id?: string;
  model_finetuning_id?: string;
  provider_id?: string;
}

/**
 * Helper class để resolve model information từ các nguồn khác nhau
 */
@Injectable()
export class ModelResolutionHelper {
  private readonly logger = new Logger(ModelResolutionHelper.name);

  constructor(
    private readonly baseModelRepository: BaseModelRepository,
    private readonly fineTuningModelRepository: FineTuningModelRepository,
    private readonly userProviderModelRepository: UserProviderModelRepository,
  ) {}

  /**
   * Resolve model information từ AgentUser entity
   * @param modelBaseId ID của base model (nếu có)
   * @param modelFinetuningId ID của finetuning model (nếu có)
   * @param modelId ID của model từ provider (nếu có)
   * @param providerId ID của provider (nếu có)
   * @returns Thông tin model đã resolve
   */
  async resolveModelInfo(
    modelBaseId?: string | null,
    modelFinetuningId?: string | null,
    modelId?: string | null,
    providerId?: string | null,
  ): Promise<ResolvedModelInfo | null> {
    try {
      // Scenario 1: model_base_id → join base_models để lấy model_id
      if (modelBaseId) {
        return await this.resolveFromBaseModel(modelBaseId);
      }

      // Scenario 2: model_finetuning_id → join fine_tuning_models để lấy model_id
      if (modelFinetuningId) {
        return await this.resolveFromFineTuningModel(modelFinetuningId);
      }

      // Scenario 3: model_id + provider_id → lấy trực tiếp
      if (modelId && providerId) {
        return await this.resolveFromUserProvider(modelId, providerId);
      }

      return null;
    } catch (error) {
      this.logger.error(`Lỗi khi resolve model info: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Resolve model từ base_models table
   * @param modelBaseId ID của base model
   * @returns Thông tin model từ base model
   */
  private async resolveFromBaseModel(modelBaseId: string): Promise<ResolvedModelInfo | null> {
    try {
      const baseModel = await this.baseModelRepository.findOne({
        where: { id: modelBaseId },
      });

      if (!baseModel) {
        this.logger.warn(`Base model không tồn tại: ${modelBaseId}`);
        return null;
      }

      return {
        model_id: baseModel.modelId,
        provider: 'system', // Base model là system model
        model_base_id: modelBaseId,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi resolve base model: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Resolve model từ fine_tuning_models table
   * @param modelFinetuningId ID của finetuning model
   * @returns Thông tin model từ finetuning model
   */
  private async resolveFromFineTuningModel(modelFinetuningId: string): Promise<ResolvedModelInfo | null> {
    try {
      const fineTuningModel = await this.fineTuningModelRepository.findOne({
        where: { id: modelFinetuningId },
      });

      if (!fineTuningModel) {
        this.logger.warn(`Fine-tuning model không tồn tại: ${modelFinetuningId}`);
        return null;
      }

      return {
        model_id: fineTuningModel.id, // Fine-tuning model sử dụng ID làm model_id
        provider: 'system', // Fine-tuning model cũng là system model
        model_finetuning_id: modelFinetuningId,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi resolve fine-tuning model: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Resolve model từ user provider model
   * @param modelId ID của model
   * @param providerId ID của provider
   * @returns Thông tin model từ user provider
   */
  private async resolveFromUserProvider(modelId: string, providerId: string): Promise<ResolvedModelInfo | null> {
    try {
      const userProviderModel = await this.userProviderModelRepository.findOne({
        where: { id: providerId },
      });

      if (!userProviderModel) {
        this.logger.warn(`User provider model không tồn tại: ${providerId}`);
        return null;
      }

      return {
        model_id: modelId,
        provider: userProviderModel.type || 'unknown',
        provider_id: providerId,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi resolve user provider model: ${error.message}`, error.stack);
      return null;
    }
  }

  /**
   * Resolve multiple models cho danh sách agents
   * @param agents Danh sách agents với model info
   * @returns Map của agent ID và model info
   */
  async resolveMultipleModels(
    agents: Array<{
      id: string;
      modelBaseId?: string | null;
      modelFinetuningId?: string | null;
      modelId?: string | null;
      providerId?: string | null;
    }>
  ): Promise<Map<string, ResolvedModelInfo | null>> {
    const result = new Map<string, ResolvedModelInfo | null>();

    // Resolve từng agent
    for (const agent of agents) {
      const modelInfo = await this.resolveModelInfo(
        agent.modelBaseId,
        agent.modelFinetuningId,
        agent.modelId,
        agent.providerId
      );
      result.set(agent.id, modelInfo);
    }

    return result;
  }
}
