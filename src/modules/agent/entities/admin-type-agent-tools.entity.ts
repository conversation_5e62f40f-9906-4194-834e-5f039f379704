import { Entity, PrimaryColumn, ManyToOne, Join<PERSON>olumn } from 'typeorm';
import { TypeAgent } from './type-agent.entity';
import { AdminTool } from '@modules/tools/entities';

/**
 * Entity đại diện cho bảng admin_type_agent_tools trong cơ sở dữ liệu
 * Bảng liên kết ánh xạ admin tools với type agent
 * Chỉ admin mới có thể tạo type-agent và liên kết với admin tools
 */
@Entity('admin_type_agent_tools')
export class AdminTypeAgentTools {
  /**
   * ID của admin tool (UUID)
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'tool_id', type: 'uuid' })
  toolId: string;

  /**
   * ID của type agent
   * Là một phần của khóa chính
   */
  @PrimaryColumn({ name: 'type_id', type: 'integer' })
  typeId: number;

  /**
   * <PERSON>uan hệ với TypeAgent
   */
  @ManyToOne(() => TypeAgent)
  @JoinColumn({ name: 'type_id' })
  typeAgent: TypeAgent;

  /**
   * <PERSON>uan hệ với AdminTool
   */
  @ManyToOne(() => AdminTool)
  @JoinColumn({ name: 'tool_id' })
  tool: AdminTool;
}
