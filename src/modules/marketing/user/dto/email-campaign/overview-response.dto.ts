import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho thống kê tổng quan email campaign
 */
export class EmailCampaignOverviewDto {
  /**
   * Tổng số chiến dịch
   * @example 150
   */
  @ApiProperty({
    description: 'Tổng số chiến dịch email',
    example: 150,
  })
  totalCampaigns: number;

  /**
   * Số chiến dịch đang gửi
   * @example 5
   */
  @ApiProperty({
    description: 'Số chiến dịch đang gửi',
    example: 5,
  })
  sendingCampaigns: number;

  /**
   * Số chiến dịch đã gửi
   * @example 120
   */
  @ApiProperty({
    description: 'Số chiến dịch đã gửi',
    example: 120,
  })
  sentCampaigns: number;

  /**
   * Số chiến dịch đã lên lịch
   * @example 25
   */
  @ApiProperty({
    description: 'Số chiến dịch đã lên lịch',
    example: 25,
  })
  scheduledCampaigns: number;

  /**
   * Thời gian cập nhật thống kê (Unix timestamp)
   * @example 1619171200
   */
  @ApiProperty({
    description: 'Thời gian cập nhật thống kê (Unix timestamp)',
    example: 1619171200,
  })
  updatedAt: number;
}

/**
 * DTO response cho API overview email campaign
 */
export class EmailCampaignOverviewResponseDto {
  @ApiProperty({
    description: 'Thống kê tổng quan email campaign',
    type: EmailCampaignOverviewDto,
  })
  overview: EmailCampaignOverviewDto;
}
