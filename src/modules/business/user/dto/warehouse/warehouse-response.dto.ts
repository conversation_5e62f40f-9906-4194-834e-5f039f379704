import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { WarehouseCustomFieldResponseDto } from './warehouse-custom-field-response.dto';

/**
 * DTO cho phản hồi thông tin kho
 */
export class WarehouseResponseDto {
  /**
   * ID của kho
   * @example 1
   */
  @Expose()
  @ApiProperty({
    description: 'ID của kho',
    example: 1,
  })
  warehouseId: number;

  /**
   * Tên kho
   * @example "Kho hàng chính"
   */
  @Expose()
  @ApiProperty({
    description: 'Tên kho',
    example: 'Kho hàng chính',
  })
  name: string;

  /**
   * Mô tả kho
   * @example "Kho chứa các sản phẩm chính của công ty"
   */
  @Expose()
  @ApiProperty({
    description: 'Mô tả kho',
    example: '<PERSON>ho chứa các sản phẩm chính của công ty',
  })
  description: string;

  /**
   * <PERSON>ạ<PERSON> kho
   * @example "PHYSICAL"
   */
  @Expose()
  @ApiProperty({
    description: 'Loại kho',
    enum: WarehouseTypeEnum,
    example: WarehouseTypeEnum.PHYSICAL,
  })
  type: WarehouseTypeEnum;

  /**
   * Metadata chứa custom fields và thông tin bổ sung
   */
  @Expose()
  @ApiProperty({
    description: 'Metadata chứa custom fields và thông tin bổ sung',
    example: {
      custom_fields: [
        {
          id: 1,
          configId: 'warehouse_location',
          label: 'Vị trí kho',
          type: 'text',
          required: false,
          configJson: { placeholder: 'Nhập vị trí kho' },
          tags: ['warehouse'],
          value: { value: 'Tầng 2, Khu A' }
        }
      ]
    },
    required: false,
  })
  metadata?: any;
}
