import {
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { TypeAgentStatus } from '@modules/agent/constants';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';

/**
 * Entity đại diện cho bảng type_agents trong cơ sở dữ liệu
 * Bảng lưu thông tin các loại agent trong hệ thống
 */
@Entity('type_agents')
export class TypeAgent {
  /**
   * ID định danh duy nhất cho mỗi loại agent
   */
  @PrimaryGeneratedColumn()
  id: number;

  /**
   * Tên loại agent
   */
  @Column({ length: 255 })
  name: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @Column({ type: 'text', nullable: true })
  description: string | null;

  /**
   * Cấu hình mặc định cho loại agent dạng JSONB
   */
  @Column({ name: 'config', type: 'jsonb', default: '{}' })
  config: TypeAgentConfig;

  /**
   * ID nhân viên tạo loại agent (nếu là loại agent của admin)
   */
  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  /**
   * ID nhân viên cập nhật loại agent
   */
  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @CreateDateColumn({
    name: 'created_at',
    type: 'bigint',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật gần nhất (timestamp millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
  })
  updatedAt: number;

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = Date.now();
  }

  /**
   * Thời điểm xóa mềm (timestamp millis)
   */
  @Column({
    name: 'deleted_at',
    type: 'bigint',
    nullable: true,
  })
  deletedAt: number;

  /**
   * ID của nhân viên xóa
   */
  @Column({ name: 'deleted_by', type: 'bigint', nullable: true })
  deletedBy: number;

  /**
   * Trạng thái của loại agent
   */
  @Column({
    type: 'enum',
    enum: TypeAgentStatus,
    default: TypeAgentStatus.DRAFT,
  })
  status: TypeAgentStatus;
}
