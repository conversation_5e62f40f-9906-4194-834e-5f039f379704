import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import {
  BaseModelRes,
  BaseModelDetailRes,
  CreateBaseModelDto,
  UpdateBaseModelDto,
  UpdateBaseModelResponseDto,
} from '@/modules/model-training/dto/base-model/base-model.dto';
import { CurrentEmployee } from '@modules/auth/decorators';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { BaseModel } from '@modules/model-training/entities';
import { MODEL_TRAINING_ERROR_CODES } from '@modules/model-training/exceptions';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { BaseModelQueryDto } from '../../dto/base-model/base-model-query.dto';
import { ModelResponse } from '../../interfaces/model-response.interface';
import { BaseModelAdminService } from '../services';
import { ListModelQueryDto } from '../dto/base-mode/list-model-query.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller handling APIs related to base model management for admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BASE_MODEL)
@ApiExtraModels(ApiResponseDto, BaseModel, PaginatedResult, ApiErrorResponseDto, BaseModelRes, BaseModelDetailRes)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/base-models')
export class BaseModelAdminController {
  constructor(
    private readonly baseModelAdminService: BaseModelAdminService,
  ) { }

  /**
   * Lấy danh sách models từ nhà cung cấp theo ID
   */
  @Get('/list')
  @UseGuards(JwtEmployeeGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Lấy danh sách models từ nhà cung cấp',
    description:
      'Lấy tất cả models từ nhà cung cấp với khả năng lọc theo loại model (GPT, embedding, davinci, v.v.)',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách models thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    MODEL_TRAINING_ERROR_CODES.INVALID_QUERY_PARAMETERS,
    MODEL_TRAINING_ERROR_CODES.API_ACCESS_ERROR,
    MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND
  )
  async getModels(
    @Query() query: ListModelQueryDto,
  ): Promise<ApiResponseDto<ModelResponse>> {
    const models = await this.baseModelAdminService.getModelsByProvider(query.providerId, query.limit);
    return ApiResponseDto.success(models);
  }

  /**
   * Creates a new base model from OpenAI
   */
  @Post('/:modelId')
  @ApiOperation({
    summary: 'Create a new base model',
    description: 'Creates a new base model from OpenAI model',
  })
  @ApiParam({
    name: 'modelId',
    description: 'OpenAI model ID (URL encoded if contains special characters)',
    example: 'gpt-4o-mini'
  })
  @ApiBody({
    type: CreateBaseModelDto,
    description: 'Base model creation data',
  })
  @ApiResponse({
    status: 201,
    description: 'Base model created successfully',
    schema: ApiResponseDto.getSchema(BaseModel),
  })
  @ApiErrorResponse(
    MODEL_TRAINING_ERROR_CODES.MODEL_ALREADY_EXISTS,
    MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA,
  )
  async createBaseModel(
    @Param('modelId') rawModelId: string,
    @Body() createBaseModelDto: CreateBaseModelDto,
    @CurrentEmployee() user: JwtPayload,
  ): Promise<ApiResponseDto<void>> {
    // Decode URL-encoded modelId để xử lý các ký tự đặc biệt
    const modelId = decodeURIComponent(rawModelId);

    const baseModel = await this.baseModelAdminService.createBaseModel(
      createBaseModelDto,
      modelId,
      user.id,
    );
    return ApiResponseDto.created(baseModel);
  }

  /**
   * Retrieves paginated list of base models
   */
  @Get()
  @ApiOperation({
    summary: 'Get list of base models',
    description:
      'Retrieves paginated list of base models with optional filtering',
  })
  // @ApiQuery({ type: QueryDto })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved list of base models',
    schema: ApiResponseDto.getPaginatedSchema(BaseModelRes),
  })
  @ApiErrorResponse(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR)
  async getAllBaseModels(
    @Query() query: BaseModelQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<BaseModelRes>>> {
    const result = await this.baseModelAdminService.findAll(query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Updates an existing base model
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Update base model',
    description: 'Updates an existing base model with new data',
  })
  @ApiParam({ name: 'id', description: 'Base model ID' })
  @ApiResponse({
    status: 200,
    description: 'Base model updated successfully',
    schema: ApiResponseDto.getSchema(UpdateBaseModelResponseDto),
  })
  @ApiErrorResponse(
    MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
    MODEL_TRAINING_ERROR_CODES.UPDATE_FAILED,
    MODEL_TRAINING_ERROR_CODES.FORBIDDEN,
  )
  async updateBaseModel(
    @Param('id') id: string,
    @Body() updateDto: UpdateBaseModelDto,
    @CurrentEmployee() user: JwtPayload,
  ): Promise<ApiResponseDto<UpdateBaseModelResponseDto>> {
    const updatedModel = await this.baseModelAdminService.updateBaseModel(
      id,
      updateDto,
      user.id,
    );
    return ApiResponseDto.success({ id: updatedModel.id });
  }

  /**
   * Retrieves a base model by its ID
   */
  @Get('/:id')
  @ApiOperation({
    summary: 'Get base model by ID',
    description: 'Retrieves details of a specific base model',
  })
  @ApiParam({ name: 'id', description: 'Base model ID' })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved base model with detailed information',
    schema: ApiResponseDto.getSchema(BaseModelDetailRes),
  })
  @ApiErrorResponse(
    MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
    MODEL_TRAINING_ERROR_CODES.MODEL_DELETED,
    MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
  )
  async getBaseModelById(
    @Param('id') id: string,
  ): Promise<ApiResponseDto<BaseModelDetailRes>> {
    const model = await this.baseModelAdminService.getModelById(id);
    return ApiResponseDto.success(model);
  }

  /**
   * Deletes (soft delete) a base model by ID
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Delete base model',
    description:
      'Marks a base model as DELETED instead of removing it from the database',
  })
  @ApiParam({ name: 'id', description: 'Base model ID' })
  @ApiResponse({
    status: 200,
    description: 'Base model deleted successfully',
    schema: ApiResponseDto.getSchema(null),
  })
  @ApiErrorResponse(
    MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
    MODEL_TRAINING_ERROR_CODES.DELETE_FAILED,
  )
  async deleteBaseModel(
    @Param('id') id: string,
    @CurrentEmployee() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.baseModelAdminService.deleteBaseModel(id, user.id);
    return ApiResponseDto.success(null, 'Base model deleted successfully');
  }

  /**
   * Approves a base model
   */
  @Patch(':id/approve')
  @ApiOperation({
    summary: 'Approve base model',
    description: 'Approves a base model and changes its status to APPROVED',
  })
  @ApiParam({ name: 'id', description: 'Base model ID' })
  @ApiResponse({
    status: 200,
    description: 'Base model approved successfully',
    schema: ApiResponseDto.getSchema(BaseModel),
  })
  @ApiErrorResponse(
    MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
    MODEL_TRAINING_ERROR_CODES.INVALID_STATUS,
  )
  async approveModel(
    @Param('id') id: string,
    @CurrentEmployee() user: JwtPayload,
  ): Promise<ApiResponseDto<null>> {
    await this.baseModelAdminService.approveModel(
      id,
      user.id,
    );
    return ApiResponseDto.success(null, 'Base model approved successfully');
  }
}
