import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayMinSize,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  MaxLength,
  ValidateNested
} from 'class-validator';

/**
 * DTO cho thông tin file trong batch creation
 */
class FileDto {
  /**
   * Tên file
   * @example "Tài liệu hướng dẫn.pdf"
   */
  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn.pdf',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên file không được để trống' })
  @IsString({ message: 'Tên file phải là chuỗi' })
  @MaxLength(255, { message: 'Tên file không được vượt quá 255 ký tự' })
  name: string;

  /**
   * <PERSON>ích thước file (byte)
   * @example 1024000
   */
  @ApiProperty({
    description: '<PERSON><PERSON>ch thước file (byte)',
    example: 1024000,
  })
  @IsNotEmpty({ message: '<PERSON>ích thước file không được để trống' })
  @IsNumber({}, { message: 'Kích thước file phải là số' })
  @IsPositive({ message: 'Kích thước file phải là số dương' })
  size: number;
}

/**
 * DTO cho việc tạo nhiều file cùng lúc
 */
export class CreateFilesMultipleDto {
  /**
   * ID người dùng (sẽ được gán tự động từ JWT)
   * @example 1
   */
  @ApiProperty({
    description: 'ID người dùng',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  @Type(() => Number)
  userId?: number;

  /**
   * ID kho ảo chứa file (tùy chọn)
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho ảo chứa file (tùy chọn)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID kho ảo phải là số' })
  @Type(() => Number)
  warehouseId?: number;

  /**
   * ID thư mục chứa file (tùy chọn - nếu không có sẽ tạo trong thư mục gốc)
   * @example 1
   */
  @ApiProperty({
    description: 'ID thư mục chứa file (tùy chọn - nếu không có sẽ tạo trong thư mục gốc)',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID thư mục phải là số' })
  @Type(() => Number)
  folderId?: number;

  /**
   * Danh sách các file cần tạo
   */
  @ApiProperty({
    description: 'Danh sách các file cần tạo',
    type: [FileDto],
    example: [
      {
        name: 'Tài liệu 1.pdf',
        size: 1024000
      },
      {
        name: 'Tài liệu 2.docx',
        size: 2048000
      }
    ]
  })
  @IsArray({ message: 'Files phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một file' })
  @ValidateNested({ each: true })
  @Type(() => FileDto)
  files: FileDto[];
}
