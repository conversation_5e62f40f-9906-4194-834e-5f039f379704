import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUrl, IsInt, Min, Max, IsBoolean, IsOptional } from 'class-validator';

/**
 * DTO cho crawl URL admin - Updated: Removed ownedBy field
 */
export class CrawlAdminDto {
  @ApiProperty({
    description: 'URL gốc để bắt đầu crawl',
    example: 'https://example.com',
  })
  @IsNotEmpty({ message: 'URL không được để trống' })
  @IsUrl({}, { message: 'URL không hợp lệ' })
  url: string;

  @ApiProperty({
    description: 'Độ sâu tìm kiếm URL (1-3)',
    example: 2,
    minimum: 1,
    maximum: 3,
  })
  @IsNotEmpty({ message: 'Độ sâu không được để trống' })
  @IsInt({ message: 'Độ sâu phải là số nguyên' })
  @Min(1, { message: '<PERSON><PERSON> sâu tối thiểu là 1' })
  @Max(3, { message: '<PERSON><PERSON> sâu tối đa là 3' })
  depth: number;

  @ApiProperty({
    description: 'Bỏ qua kiểm tra robots.txt (mặc định: false)',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'ignoreRobotsTxt phải là giá trị boolean' })
  ignoreRobotsTxt?: boolean;

  @ApiProperty({
    description: 'Số lượng URL tối đa để crawl (1-100, mặc định: 20)',
    example: 20,
    required: false,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsInt({ message: 'maxUrls phải là số nguyên' })
  @Min(1, { message: 'maxUrls tối thiểu là 1' })
  @Max(100, { message: 'maxUrls tối đa là 100' })
  maxUrls?: number;
}

/**
 * DTO cho bắt đầu crawl với tracking session (Admin)
 */
export class StartCrawlWithTrackingAdminDto extends CrawlAdminDto {
  // Kế thừa tất cả từ CrawlAdminDto
}

/**
 * DTO response khi bắt đầu crawl (Admin)
 */
export class StartCrawlAdminResponseDto {
  @ApiProperty({
    description: 'ID của session crawl',
    example: 'crawl_admin_1_1234567890',
  })
  sessionId: string;

  @ApiProperty({
    description: 'ID của job trong queue',
    example: '12345',
    nullable: true
  })
  jobId: string | number | undefined;

  @ApiProperty({
    description: 'Thông báo',
    example: 'Tiếp nhận yêu cầu crawl. Sử dụng sessionId để theo dõi tiến độ.',
  })
  message: string;
}

/**
 * DTO response cho tiến độ crawl (Admin)
 */
export class CrawlProgressAdminResponseDto {
  @ApiProperty({
    description: 'ID của session',
    example: 'crawl_admin_1_1234567890',
  })
  sessionId: string;

  @ApiProperty({
    description: 'URL được crawl',
    example: 'https://example.com',
  })
  url: string;

  @ApiProperty({
    description: 'Trạng thái crawl',
    example: 'running',
    enum: ['running', 'completed', 'error', 'cancelled'],
  })
  status: string;

  @ApiProperty({
    description: 'Thông tin tiến độ',
    example: {
      totalUrls: 30,
      processedUrls: 15,
      successfulUrls: 12,
      failedUrls: 3,
      currentDepth: 2,
      percentage: 50,
      currentUrl: 'https://example.com/page1',
      estimatedTimeRemaining: 120,
    },
  })
  progress: any;

  @ApiProperty({
    description: 'Cấu hình crawl',
    example: {
      depth: 3,
      maxUrls: 30,
      ignoreRobotsTxt: true,
    },
  })
  config: any;

  @ApiProperty({
    description: 'Kết quả crawl',
    example: {
      urlsProcessed: 15,
      urlsSaved: 12,
      message: 'Đang crawl...',
    },
    nullable: true,
  })
  result: any;

  @ApiProperty({
    description: 'Danh sách lỗi',
    example: null,
    nullable: true,
  })
  errors: any;

  @ApiProperty({
    description: 'Thời gian bắt đầu (timestamp)',
    example: '1234567890',
  })
  startTime: string | number;

  @ApiProperty({
    description: 'Thời gian kết thúc (timestamp)',
    example: '1234567890',
    nullable: true,
  })
  endTime: string | number;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: '2023-01-01T00:00:00.000Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Metadata bổ sung',
    example: { source: 'admin' },
  })
  metadata: any;
}

/**
 * DTO response cho danh sách sessions crawl (Admin)
 */
export class CrawlSessionListAdminResponseDto {
  @ApiProperty({
    description: 'Danh sách sessions',
    type: [Object],
  })
  sessions: Array<{
    sessionId: string;
    url: string;
    status: string;
    percentage: number;
    processedUrls: number;
    totalUrls: number;
    createdAt: Date;
    endTime: string | number;
    message: string;
  }>;

  @ApiProperty({
    description: 'Tổng số sessions',
    example: 100,
  })
  total: number;

  @ApiProperty({
    description: 'Trang hiện tại',
    example: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Số items mỗi trang',
    example: 20,
  })
  limit: number;

  @ApiProperty({
    description: 'Tổng số trang',
    example: 5,
  })
  totalPages: number;
}
