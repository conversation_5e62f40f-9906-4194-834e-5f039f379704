import { PaginatedResult } from '@/common/response';
import { BaseModelStatusEnum } from '@/modules/model-training/constants/base-model-status.enum';
import { TypeProviderEnum } from '@/modules/model-training/constants/type-provider.enum';
import {
  BaseModelDetailRes,
  BaseModelRes,
  CreateBaseModelDto,
  DeletedBaseModelRes,
  UpdateBaseModelDto
} from '@/modules/model-training/dto/base-model/base-model.dto';
import { AiProviderHelper } from '@/shared/services/ai/helpers/ai-provider.helper';
import { AppException } from '@common/exceptions';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { AdminProviderModelRepository, BaseModelRepository } from '@modules/model-training/repositories';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { Transactional } from 'typeorm-transactional';
import { BaseModelDeletedQueryDto } from '../../dto/base-model/base-model-deleted-query.dto';
import { BaseModelQueryDto } from '../../dto/base-model/base-model-query.dto';
import { BaseModel } from '../../entities/base-model.entity';
import { MODEL_TRAINING_ERROR_CODES } from '../../exceptions';
import { ApiKeyEncryptionHelper } from '../../helpers/api-key-encryption.helper';
import { BaseModelValidationHelper } from '../../helpers/base-model-validation.helper';
import { ModelResponse } from '../../interfaces/model-response.interface';

@Injectable()
export class BaseModelAdminService {
  private readonly logger = new Logger(BaseModelAdminService.name);

  constructor(
    private readonly baseModelRepository: BaseModelRepository,
    private readonly modelValidationHelper: BaseModelValidationHelper,
    private readonly cdnService: CdnService,
    private readonly adminProviderModelRepository: AdminProviderModelRepository,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,
    private readonly aiProviderHelper: AiProviderHelper,
    private readonly employeeInfoService: EmployeeInfoService,
  ) { }

  /**
   * Creates a new base model record in the database
   * @param createBaseModelDto Base model data from admin
   * @param modelId Model ID (from OpenAI)
   * @param userId User ID creating the model
   * @returns Created base model
   * @throws AppException if model already exists or provider not found
   */
  @Transactional()
  async createBaseModel(
    createBaseModelDto: CreateBaseModelDto,
    modelId: string,
    userId: number,
  ): Promise<void> {
    // Kiểm tra xem model đã tồn tại chưa dựa trên cả modelId và providerId
    const existingModel = await this.baseModelRepository.findByModelIdAndProviderId(
      modelId,
      createBaseModelDto.providerId
    );

    if (existingModel) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.MODEL_ALREADY_EXISTS,
        `Model với ID ${modelId} đã tồn tại cho provider ${createBaseModelDto.providerId}`
      );
    }

    const now = Date.now();
    const { type, apiKey } = await this.adminProviderModelRepository.findTypeAndApiKeyById(createBaseModelDto.providerId);

    if (!apiKey) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        'Provider not found',
      );
    }

    // Sử dụng helper để kiểm tra model
    await this.aiProviderHelper.retrieveModel(modelId, type, apiKey, true);


    // Tạo instance của BaseModel với các thuộc tính phù hợp với entity mới
    const baseModel = new BaseModel();
    baseModel.modelId = modelId; // Sử dụng modelId thay vì name
    baseModel.description = createBaseModelDto.description || null;
    baseModel.providerId = createBaseModelDto.providerId;
    baseModel.baseInputRate = createBaseModelDto.baseInputRate;
    baseModel.baseOutputRate = createBaseModelDto.baseOutputRate;
    baseModel.baseTrainRate = createBaseModelDto.baseTrainRate;
    baseModel.fineTuningInputRate = createBaseModelDto.fineTuningInputRate;
    baseModel.fineTuningOutputRate = createBaseModelDto.fineTuningOutputRate;
    baseModel.fineTuningTrainRate = createBaseModelDto.fineTuningTrainRate;
    baseModel.tokenCount = createBaseModelDto.tokenCount;

    baseModel.createdBy = userId;
    baseModel.updatedBy = userId;
    baseModel.createdAt = now;
    baseModel.updatedAt = now;
    baseModel.status = createBaseModelDto.status || BaseModelStatusEnum.DRAFT;

    // Lưu model vào database
    await this.baseModelRepository.save(baseModel);
  }

  async getModelsByProvider(
    providerId: string,
    limit?: number,
  ): Promise<ModelResponse> {
    const { type, apiKey } = await this.adminProviderModelRepository.findTypeAndApiKeyById(providerId);

    if (!apiKey) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        'Provider not found',
      );
    }

    return await this.aiProviderHelper.getModels(type, apiKey, limit, true);
  }

  /**
   * Finds all base models with pagination and search
   * @param query Query parameters for pagination and search
   * @returns Paginated list of base models
   */
  async findAll(
    query: BaseModelQueryDto,
  ): Promise<PaginatedResult<BaseModelRes>> {

    const result = (await this.baseModelRepository.findAllByAdmin(query));
    return {
      items: result.items,
      meta: result.meta,
    }
  }

  /**
   * Updates an existing base model
   * @param id Base model ID
   * @param updateDto Update data
   * @param userId User ID performing the update
   * @returns Updated base model
   * @throws AppException if base model not found
   */
  @Transactional()
  async updateBaseModel(
    id: string,
    updateDto: UpdateBaseModelDto,
    userId: number,
  ): Promise<BaseModel> {
    try {
      const now = Date.now();
      // Sử dụng findById để đảm bảo chỉ lấy model chưa bị xóa
      const baseModel = await this.baseModelRepository.findById(id);

      // Kiểm tra xem model có tồn tại không
      if (!baseModel) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
          `Không tìm thấy base model với ID ${id} hoặc model đã bị xóa`
        );
      }

      // Cập nhật thông tin model
      Object.assign(baseModel, updateDto);
      baseModel.updatedAt = now;
      baseModel.updatedBy = userId;

      // Lưu model đã cập nhật
      const updatedModel = await this.baseModelRepository.save(baseModel);

      return updatedModel;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật base model ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi cập nhật base model: ${error.message}`
      );
    }
  }

  /**
   * Soft-delete an existing model
   * @param id Base model ID
   * @param userId User ID performing the delete
   * @returns return the notice
   * @throws AppException if base model not found
   */
  async deleteBaseModel(id: string, userId: number) {
    try {
      // Gọi phương thức deleteBaseModel từ repository
      const deleted = await this.baseModelRepository.deleteBaseModel(id, userId);

      if (!deleted) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
          `Không tìm thấy base model với ID ${id} hoặc không thể xóa`
        );
      }

      return null;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa base model ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DELETE_FAILED,
        `Lỗi khi xóa base model: ${error.message}`
      );
    }
  }


  /**
   * Phê duyệt một base model, chuyển trạng thái từ DRAFT sang APPROVED
   * @param id ID của base model cần phê duyệt
   * @param userId ID của người thực hiện phê duyệt
   * @returns Thông tin base model sau khi được phê duyệt
   */
  async approveModel(id: string, userId: number) {
    try {
      // Gọi phương thức approveModel từ repository
      const updatedModel = await this.baseModelRepository.approveModel(id, userId);

      // Kiểm tra kết quả
      if (!updatedModel) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
          `Không tìm thấy base model với ID ${id} ở trạng thái DRAFT hoặc không thể phê duyệt`
        );
      }

      return updatedModel;
    } catch (error) {
      this.logger.error(`Lỗi khi phê duyệt base model ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi phê duyệt base model: ${error.message}`
      );
    }
  }

  /**
   * Lấy danh sách base model đã xóa
   * @param query Tham số truy vấn cho phân trang và tìm kiếm
   * @returns Danh sách base model đã xóa với phân trang
   */
  async findAllDeleted(query: BaseModelDeletedQueryDto): Promise<PaginatedResult<DeletedBaseModelRes>> {
    try {
      return await this.baseModelRepository.findAllDeletedByAdmin(query);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách base model đã xóa: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
        `Lỗi khi lấy danh sách base model đã xóa: ${error.message}`
      );
    }
  }

  /**
   * Khôi phục base model đã xóa
   * @param id ID của base model cần khôi phục
   * @param userId ID của người thực hiện khôi phục
   * @returns Thông báo khôi phục thành công
   */
  async restoreBaseModel(id: string, userId: number): Promise<void> {
    try {
      const restored = await this.baseModelRepository.restoreBaseModel(id, userId);

      if (!restored) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
          `Không tìm thấy base model đã xóa với ID ${id} hoặc không thể khôi phục`
        );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi khôi phục base model ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.UPDATE_FAILED,
        `Lỗi khi khôi phục base model: ${error.message}`
      );
    }
  }

  /**
   * Lấy thông tin chi tiết của một base model theo ID (UUID)
   * @param id UUID của base model
   * @returns Thông tin chi tiết của base model bao gồm thông tin người tạo và người cập nhật
   */
  async getModelById(id: string): Promise<BaseModelDetailRes> {
    try {
      // Lấy thông tin chi tiết của base model từ repository
      const { model, providerName, providerType, isDeleted } = await this.baseModelRepository.getModelDetailById(id);

      if (!model) {
        if (isDeleted) {
          // Nếu model đã bị xóa, trả về lỗi cụ thể
          throw new AppException(
            MODEL_TRAINING_ERROR_CODES.MODEL_DELETED,
            `Base model với ID ${id} đã bị xóa`
          );
        }
        throw new NotFoundException('Base model not found');
      }

      // Chuyển đổi từ BaseModel sang BaseModelDetailRes
      const baseModelDetailRes = new BaseModelDetailRes();
      baseModelDetailRes.id = model.id;
      baseModelDetailRes.modelId = model.modelId; // ✅ Thêm missing field
      baseModelDetailRes.description = model.description;
      baseModelDetailRes.providerId = model.providerId;
      baseModelDetailRes.providerName = providerName;
      baseModelDetailRes.providerType = providerType as TypeProviderEnum;
      baseModelDetailRes.status = model.status;
      baseModelDetailRes.createAt = model.createdAt;
      baseModelDetailRes.updateAt = model.updatedAt;


      // Lấy thông tin người tạo nếu có
      if (model.createdBy) {
        try {
          const creatorInfo = await this.employeeInfoService.getEmployeeInfo(model.createdBy);
          baseModelDetailRes.creator = {
            id: creatorInfo.employeeId,
            name: creatorInfo.name,
            avatar: creatorInfo.avatar || undefined
          };
        } catch (error) {
          this.logger.warn(`Không thể lấy thông tin người tạo cho base model ${id}: ${error.message}`);
        }
      }

      // Lấy thông tin người cập nhật nếu có (luôn trả về thông tin người sửa nếu có)
      if (model.updatedBy) {
        try {
          const editorInfo = await this.employeeInfoService.getEmployeeInfo(model.updatedBy);
          baseModelDetailRes.editor = {
            id: editorInfo.employeeId,
            name: editorInfo.name,
            avatar: editorInfo.avatar || undefined
          };
        } catch (error) {
          this.logger.warn(`Không thể lấy thông tin người cập nhật cho base model ${id}: ${error.message}`);
        }
      }

      // Không cần xử lý thông tin người xóa vì chúng ta chỉ lấy model chưa bị xóa

      return baseModelDetailRes;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin chi tiết base model ${id}: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR, `Lỗi khi lấy thông tin chi tiết base model: ${error.message}`);
    }
  }

  /**
   * Lấy thông tin chi tiết của một base model theo modelId (string)
   * @param modelId ModelId của base model (ví dụ: "gpt-4o-realtime-preview-2024-12-17")
   * @returns Thông tin chi tiết của base model bao gồm thông tin người tạo và người cập nhật
   */
  async getModelByModelId(modelId: string): Promise<BaseModelDetailRes> {
    try {
      // Lấy thông tin chi tiết của base model từ repository
      const { model, providerName, providerType, isDeleted } = await this.baseModelRepository.getModelDetailByModelId(modelId);

      if (!model) {
        if (isDeleted) {
          // Nếu model đã bị xóa, trả về lỗi cụ thể
          throw new AppException(
            MODEL_TRAINING_ERROR_CODES.MODEL_DELETED,
            `Base model với modelId ${modelId} đã bị xóa`
          );
        }
        throw new NotFoundException('Base model not found');
      }

      // Chuyển đổi từ BaseModel sang BaseModelDetailRes
      const baseModelDetailRes = new BaseModelDetailRes();
      baseModelDetailRes.id = model.id;
      baseModelDetailRes.modelId = model.modelId;
      baseModelDetailRes.description = model.description;
      baseModelDetailRes.providerId = model.providerId;
      baseModelDetailRes.providerName = providerName;
      baseModelDetailRes.providerType = providerType as TypeProviderEnum;
      baseModelDetailRes.status = model.status;

      baseModelDetailRes.createAt = model.createdAt;
      baseModelDetailRes.updateAt = model.updatedAt;

      // Lấy thông tin người tạo nếu có
      if (model.createdBy) {
        try {
          const creatorInfo = await this.employeeInfoService.getEmployeeInfo(model.createdBy);
          baseModelDetailRes.creator = {
            id: creatorInfo.employeeId,
            name: creatorInfo.name,
            avatar: creatorInfo.avatar || undefined
          };
        } catch (error) {
          this.logger.warn(`Không thể lấy thông tin người tạo cho base model ${modelId}: ${error.message}`);
        }
      }

      // Lấy thông tin người cập nhật nếu có
      if (model.updatedBy) {
        try {
          const editorInfo = await this.employeeInfoService.getEmployeeInfo(model.updatedBy);
          baseModelDetailRes.editor = {
            id: editorInfo.employeeId,
            name: editorInfo.name,
            avatar: editorInfo.avatar || undefined
          };
        } catch (error) {
          this.logger.warn(`Không thể lấy thông tin người cập nhật cho base model ${modelId}: ${error.message}`);
        }
      }

      return baseModelDetailRes;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy thông tin chi tiết base model với modelId ${modelId}: ${error.message}`, error.stack);
      throw new AppException(MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR, `Lỗi khi lấy thông tin chi tiết base model: ${error.message}`);
    }
  }
}
