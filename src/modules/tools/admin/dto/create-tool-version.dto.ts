import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, Matches, MaxLength } from 'class-validator';
import { ToolStatusEnum } from '../../constants/tool-status.enum';

/**
 * DTO cho việc tạo mới phiên bản tool
 */
export class CreateToolVersionDto {
  @ApiProperty({
    description: 'Tên hàm trong định nghĩa code (chỉ chứa a-z, A-Z, 0-9, _)',
    example: 'searchTool',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Tên hàm chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
  })
  @MaxLength(64)
  toolName: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết về chức năng của hàm',
    example: 'Tìm kiếm thông tin từ nhiều nguồn dữ liệu khác nhau',
    required: false,
  })
  @IsString()
  @IsOptional()
  toolDescription?: string;

  @ApiProperty({
    description: 'Tham số của hàm',
    example: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Câu truy vấn tìm kiếm',
        },
      },
      required: ['query'],
    },
  })
  @IsObject()
  @IsNotEmpty()
  parameters: Record<string, any>;

  @ApiProperty({
    description: 'Mô tả những thay đổi so với phiên bản trước',
    example: 'Thêm tham số lọc theo ngày',
    required: false,
  })
  @IsString()
  @IsOptional()
  changeDescription?: string;

  @ApiProperty({
    description: 'Trạng thái của phiên bản',
    enum: ToolStatusEnum,
    default: ToolStatusEnum.DRAFT,
    required: false,
  })
  @IsEnum(ToolStatusEnum)
  @IsOptional()
  status?: ToolStatusEnum = ToolStatusEnum.DRAFT;

  @ApiProperty({
    description: 'Tên phiên bản hiển thị',
    example: 'v1.0.0',
    required: false,
  })
  @IsString()
  @IsOptional()
  versionName: string;

  @ApiProperty({
    description: 'Đánh dấu version này là version mặc định',
    example: false,
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'isDefault phải là boolean' })
  isDefault?: boolean;
}
