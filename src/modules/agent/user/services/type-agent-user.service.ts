import { Injectable, Logger } from '@nestjs/common';
import { TypeAgent } from '@modules/agent/entities';
import { TypeAgentRepository } from '@modules/agent/repositories';
import { TypeAgentStatus } from '@modules/agent/constants';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { PaginatedResult } from '@common/response';
import {
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
} from '../dto';
import { TypeAgentToolDto } from '../dto/type-agent/type-agent-tool.dto';
// TypeAgentStatus không còn được sử dụng

/**
 * Service xử lý các thao tác liên quan đến loại agent cho người dùng
 */
@Injectable()
export class TypeAgentUserService {
  private readonly logger = new Logger(TypeAgentUserService.name);

  constructor(
    private readonly typeAgentRepository: TypeAgentRepository,
  ) {}

  /**
   * L<PERSON>y danh sách loại agent có phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent có phân trang
   */
  async getTypeAgents(
    userId: number,
    queryDto: TypeAgentQueryDto,
  ): Promise<PaginatedResult<TypeAgentListItemDto>> {
    try {
      // Lấy danh sách loại agent của admin đã ACTIVE và loại agent của user
      const result = await this.typeAgentRepository.findPaginatedByQuery(queryDto, userId);

      // Chuyển đổi kết quả sang DTO với countTool (đã có sẵn từ repository)
      const items = result.items.map((item) => this.mapToTypeAgentListItemDto(item));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED, error.message);
    }
  }

  /**
   * Lấy chi tiết loại agent
   * @param id ID của loại agent
   * @param userId ID của người dùng
   * @returns Chi tiết loại agent
   */
  async getTypeAgentDetail(id: number, userId: number): Promise<TypeAgentDetailDto> {
    try {
      // Lấy thông tin loại agent
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra quyền truy cập: user chỉ được xem type-agent có status APPROVED
      if (typeAgent.status !== TypeAgentStatus.APPROVED) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Chuyển đổi kết quả sang DTO với tools
      const result = await this.mapToTypeAgentDetailDto(typeAgent, userId);

      return result;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy chi tiết loại agent: ${error.message}`);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED, error.message);
    }
  }

  /**
   * User không thể tạo type-agent
   * Chỉ admin mới có thể tạo type-agent
   */
  async createTypeAgent(userId: number, _createDto: any): Promise<number> {
    this.logger.warn(`User ${userId} cố gắng tạo type-agent nhưng không được phép`);
    throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_CREATION_FAILED);
  }

  /**
   * User không thể cập nhật type-agent
   * Chỉ admin mới có thể cập nhật type-agent
   */
  async updateTypeAgent(
    id: number,
    userId: number,
    _updateDto: any,
  ): Promise<void> {
    this.logger.warn(`User ${userId} cố gắng cập nhật type-agent ${id} nhưng không được phép`);
    throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
  }

  /**
   * User không thể xóa type-agent
   * Chỉ admin mới có thể xóa type-agent
   */
  async deleteTypeAgent(id: number, userId: number): Promise<void> {
    this.logger.warn(`User ${userId} cố gắng xóa type-agent ${id} nhưng không được phép`);
    throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param typeAgent Entity TypeAgent với toolCount
   * @returns TypeAgentListItemDto
   */
  private mapToTypeAgentListItemDto(typeAgent: TypeAgent & { toolCount: number }): TypeAgentListItemDto {
    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      config: typeAgent.config,
      countTool: typeAgent.toolCount,
      createdAt: typeAgent.createdAt,
    };
  }

  /**
   * Chuyển đổi từ entity sang DTO chi tiết
   * @param typeAgent Entity TypeAgent
   * @param userId ID của user
   * @returns TypeAgentDetailDto
   */
  private async mapToTypeAgentDetailDto(typeAgent: TypeAgent, userId: number): Promise<TypeAgentDetailDto> {
    // Lấy danh sách user tools của type agent
    const toolsData = await this.typeAgentRepository.getToolsByTypeAgentId(typeAgent.id, userId);

    // Chuyển đổi tools data sang DTO (chỉ các field cần thiết)
    const tools: TypeAgentToolDto[] = toolsData.map(tool => ({
      id: tool.id,
      name: tool.name,
      description: tool.description,
      versionName: tool.versionName,
    }));

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      createdAt: typeAgent.createdAt,
      config: typeAgent.config,
      countTool: tools.length,
      updatedAt: typeAgent.updatedAt,
      tools,
    };
  }
}
