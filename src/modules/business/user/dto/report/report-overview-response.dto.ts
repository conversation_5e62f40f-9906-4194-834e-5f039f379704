import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho dữ liệu so sánh với kỳ trước
 */
export class PreviousPeriodDataDto {
  @Expose()
  @ApiProperty({
    description: 'Tổng doanh thu kỳ trước',
    example: 15000000,
  })
  totalRevenue: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng đơn hàng kỳ trước',
    example: 45,
  })
  totalOrders: number;

  @Expose()
  @ApiProperty({
    description: 'Khách hàng mới kỳ trước',
    example: 12,
  })
  newCustomers: number;

  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ tăng trưởng doanh thu (%)',
    example: 25.5,
  })
  revenueGrowth: number;

  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ tăng trưởng đơn hàng (%)',
    example: 15.2,
  })
  ordersGrowth: number;

  @Expose()
  @ApiProperty({
    description: 'Tỷ lệ tăng trưởng khách hàng (%)',
    example: 33.3,
  })
  customersGrowth: number;
}

/**
 * DTO cho response API tổng quan báo cáo
 */
export class ReportOverviewResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Tổng doanh thu',
    example: 20000000,
  })
  totalRevenue: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng đơn hàng',
    example: 52,
  })
  totalOrders: number;

  @Expose()
  @ApiProperty({
    description: 'Khách hàng mới',
    example: 16,
  })
  newCustomers: number;

  @Expose()
  @ApiProperty({
    description: 'Khoảng thời gian báo cáo',
    example: 'month',
  })
  period: string;

  @Expose()
  @ApiProperty({
    description: 'Ngày bắt đầu',
    example: '2024-01-01',
  })
  startDate: string;

  @Expose()
  @ApiProperty({
    description: 'Ngày kết thúc',
    example: '2024-12-31',
  })
  endDate: string;

  @Expose()
  @ApiProperty({
    description: 'Dữ liệu so sánh với kỳ trước',
    type: PreviousPeriodDataDto,
    nullable: true,
  })
  previousPeriod?: PreviousPeriodDataDto;
}
