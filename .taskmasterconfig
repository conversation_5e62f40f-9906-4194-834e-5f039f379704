{"models": {"main": {"provider": "xai", "modelId": "grok-3", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "xai", "modelId": "grok-3", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "xai", "modelId": "grok-3", "maxTokens": 8192, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/", "userId": "**********"}}