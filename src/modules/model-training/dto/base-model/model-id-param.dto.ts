import { IsString, IsNotEmpty, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO để validate modelId trong URL param
 */
export class ModelIdParamDto {
  /**
   * Model ID từ URL param
   * Cho phép các ký tự: a-z, A-Z, 0-9, d<PERSON><PERSON> gạch ngang, dấ<PERSON> chấm, dấu gạch dưới, dấu hai chấm
   */
  @ApiProperty({
    description: 'Model ID (có thể chứa ký tự đặc biệt)',
    example: 'gpt-4o-mini',
    pattern: '^[a-zA-Z0-9._:-]+$'
  })
  @IsNotEmpty({ message: 'Model ID không được để trống' })
  @IsString({ message: 'Model ID phải là chuỗi' })
  @Matches(/^[a-zA-Z0-9._:\/-]+$/, {
    message: 'Model ID chỉ được chứa chữ cái, s<PERSON>, dấ<PERSON> gạch ngang, d<PERSON><PERSON> chấ<PERSON>, d<PERSON><PERSON> g<PERSON><PERSON> dướ<PERSON>, dấ<PERSON> hai chấm và dấu gạch chéo'
  })
  modelId: string;
}
