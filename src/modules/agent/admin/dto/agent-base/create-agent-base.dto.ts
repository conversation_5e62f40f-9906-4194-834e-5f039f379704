import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ModelConfigDto } from '@modules/agent/admin/dto/common';
import { IsValidAdminModelConfig } from '@modules/agent/validators/model-config.validator';

/**
 * DTO cho việc tạo agent base mới
 */
export class CreateAgentBaseDto {
  /**
   * Tên hiển thị của agent
   */
  @ApiProperty({
    description: 'Tên hiển thị của agent',
    example: 'System Assistant',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  /**
   * MIME type của avatar (nếu cần tạo avatar)
   */
  @ApiPropertyOptional({
    description: 'MIME type của avatar (nếu cần tạo avatar)',
    example: 'image/jpeg',
  })
  @IsString()
  @IsOptional()
  avatarMimeType?: string;

  /**
   * Cấu hình model AI
   */
  @ApiProperty({
    description: 'Cấu hình model AI',
    type: ModelConfigDto,
  })
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  modelConfig: ModelConfigDto;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example:
      'Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc',
  })
  @IsString()
  @IsOptional()
  instruction?: string | null;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store',
    example: 'vector-store-1',
  })
  @IsString()
  @IsOptional()
  vectorStoreId?: string;



  /**
   * Trạng thái active của agent base
   */
  @ApiPropertyOptional({
    description: 'Trạng thái active của agent base',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  active?: boolean;



  /**
   * ID của base model (system model)
   */
  @ApiPropertyOptional({
    description: 'ID của base model (system model) - phải có ít nhất 1 trong 2: model_base_id hoặc model_finetuning_id',
    example: 'base-model-uuid',
  })
  @IsString()
  @IsOptional()
  model_base_id?: string;

  /**
   * ID của finetuning model
   */
  @ApiPropertyOptional({
    description: 'ID của finetuning model - phải có ít nhất 1 trong 2: model_base_id hoặc model_finetuning_id',
    example: 'finetuning-model-uuid',
  })
  @IsString()
  @IsOptional()
  @IsValidAdminModelConfig()
  model_finetuning_id?: string;
}
