import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho một khách hàng tiềm năng
 */
export class PotentialCustomerItemDto {
  @Expose()
  @ApiProperty({
    description: 'ID khách hàng',
    example: 1,
  })
  customerId: number;

  @Expose()
  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn <PERSON>n <PERSON>',
  })
  customerName: string;

  @Expose()
  @ApiProperty({
    description: 'Email khách hàng',
    example: '<EMAIL>',
  })
  email: string;

  @Expose()
  @ApiProperty({
    description: 'Số điện thoại',
    example: '0912345678',
    nullable: true,
  })
  phone?: string;

  @Expose()
  @ApiProperty({
    description: 'URL ảnh đại diện',
    example: 'https://example.com/avatar.jpg',
    nullable: true,
  })
  avatarUrl?: string;

  @Expose()
  @ApiProperty({
    description: 'Tổng số đơn hàng',
    example: 5,
  })
  totalOrders: number;

  @Expose()
  @ApiProperty({
    description: 'Tổng số tiền đã chi',
    example: 2500000,
  })
  totalSpent: number;

  @Expose()
  @ApiProperty({
    description: 'Giá trị đơn hàng trung bình',
    example: 500000,
  })
  averageOrderValue: number;

  @Expose()
  @ApiProperty({
    description: 'Ngày đặt đơn hàng cuối cùng',
    example: '2024-01-15',
  })
  lastOrderDate: string;

  @Expose()
  @ApiProperty({
    description: 'Số ngày từ đơn hàng cuối cùng',
    example: 30,
  })
  daysSinceLastOrder: number;

  @Expose()
  @ApiProperty({
    description: 'Điểm tiềm năng (0-100)',
    example: 85,
  })
  potentialScore: number;

  @Expose()
  @ApiProperty({
    description: 'Các thẻ tag của khách hàng',
    example: ['VIP', 'Khách hàng thân thiết'],
    type: [String],
  })
  tags: string[];
}

/**
 * DTO cho metadata của danh sách khách hàng tiềm năng
 */
export class PotentialCustomersMetaDto {
  @Expose()
  @ApiProperty({
    description: 'Tổng số khách hàng tiềm năng',
    example: 50,
  })
  totalItems: number;

  @Expose()
  @ApiProperty({
    description: 'Điểm tiềm năng trung bình',
    example: 72.5,
  })
  averagePotentialScore: number;
}

/**
 * DTO cho response API khách hàng tiềm năng
 */
export class PotentialCustomersResponseDto {
  @Expose()
  @ApiProperty({
    description: 'Danh sách khách hàng tiềm năng',
    type: [PotentialCustomerItemDto],
  })
  data: PotentialCustomerItemDto[];

  @Expose()
  @ApiProperty({
    description: 'Metadata',
    type: PotentialCustomersMetaDto,
  })
  meta: PotentialCustomersMetaDto;
}
