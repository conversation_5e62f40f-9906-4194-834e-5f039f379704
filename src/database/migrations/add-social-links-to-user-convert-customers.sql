-- Migration: Add social links columns to user_convert_customers table
-- Date: 2025-01-20
-- Description: Thêm các cột social links (facebook_link, twitter_link, linkedin_link, zalo_link, website_link) vào bảng user_convert_customers

-- Thêm cột facebook_link
ALTER TABLE user_convert_customers 
ADD COLUMN IF NOT EXISTS facebook_link VARCHAR(500) NULL;

-- Thêm cột twitter_link
ALTER TABLE user_convert_customers 
ADD COLUMN IF NOT EXISTS twitter_link VARCHAR(500) NULL;

-- Thêm cột linkedin_link
ALTER TABLE user_convert_customers 
ADD COLUMN IF NOT EXISTS linkedin_link VARCHAR(500) NULL;

-- Thêm cột zalo_link
ALTER TABLE user_convert_customers 
ADD COLUMN IF NOT EXISTS zalo_link VARCHAR(500) NULL;

-- Thêm cột website_link
ALTER TABLE user_convert_customers 
ADD COLUMN IF NOT EXISTS website_link VARCHAR(500) NULL;

-- Thê<PERSON> comment cho các cột
COMMENT ON COLUMN user_convert_customers.facebook_link IS 'Link Facebook của khách hàng';
COMMENT ON COLUMN user_convert_customers.twitter_link IS 'Link Twitter của khách hàng';
COMMENT ON COLUMN user_convert_customers.linkedin_link IS 'Link LinkedIn của khách hàng';
COMMENT ON COLUMN user_convert_customers.zalo_link IS 'Link Zalo của khách hàng';
COMMENT ON COLUMN user_convert_customers.website_link IS 'Link Website của khách hàng';

-- Kiểm tra kết quả
SELECT 
    column_name, 
    data_type, 
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'user_convert_customers' 
AND column_name IN ('facebook_link', 'twitter_link', 'linkedin_link', 'zalo_link', 'website_link')
ORDER BY column_name;
