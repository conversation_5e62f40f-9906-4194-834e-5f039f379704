import { Global, Module } from "@nestjs/common";
import { HttpModule } from "@nestjs/axios";

import { PlaceholderInjectorService } from "./services/placeholder-injector.service";
import { SendWithTemplateService } from "./services/send-with-template.service";
import { TemplateService } from "./services/template.service";
import { EmailService } from "./services/email.service";
import { EmailPlaceholderService } from "./services/email-placeholder.service";
import { SystemEmailService } from "./services/system-email.service";

import { SystemEmailTestController } from "./controllers/system-email-test.controller";
import { EmailDebugController } from "./controllers/email-debug.controller";
import { InitialEntityService } from "./services/initial-entity.service";
import { UserModule } from "../user/user.module";
import { MarketingAdminModule } from "../marketing/admin/marketing-admin.module";

import { QueueModule } from "@shared/queue/queue.module";
import { EmailClientService } from "./services/email-client.service";

@Global()
@Module({
  imports: [
    HttpModule,
    UserModule,
    MarketingAdminModule,
    QueueModule,

  ],
  controllers: [
    SystemEmailTestController,
    EmailDebugController
  ],
  providers: [
    PlaceholderInjectorService,
    SendWithTemplateService,
    TemplateService,
    EmailService,
    EmailPlaceholderService,
    SystemEmailService,

    InitialEntityService,
    EmailClientService, // Thêm EmailClientService vào providers
  ],
  exports: [
    PlaceholderInjectorService,
    SendWithTemplateService,
    TemplateService,
    EmailService,
    EmailPlaceholderService,
    SystemEmailService,
    InitialEntityService,
    EmailClientService, // Thêm EmailClientService vào exports
  ],
})
export class EmailModule {}