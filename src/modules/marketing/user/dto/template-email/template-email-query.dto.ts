import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsEnum } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto';

/**
 * Enum cho trạng thái template email
 */
export enum TemplateEmailStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  ARCHIVED = 'ARCHIVED',
}

/**
 * DTO para consultar templates de email con paginación y filtros
 */
export class TemplateEmailQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Filtrar por nombre del template',
    example: 'bienvenida',
    required: false,
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: 'Filtrar por tag',
    example: 'registro',
    required: false,
  })
  @IsString()
  @IsOptional()
  tag?: string;

  @ApiProperty({
    description: 'Lọc theo trạng thái template',
    enum: TemplateEmailStatus,
    example: TemplateEmailStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(TemplateEmailStatus)
  status?: TemplateEmailStatus;

  constructor() {
    super();
    this.sortBy = 'createdAt';
    this.sortDirection = SortDirection.DESC;
  }
}
