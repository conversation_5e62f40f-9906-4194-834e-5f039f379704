import { Column, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { UserProduct } from './user-product.entity';


/**
 * Entity đại diện cho bảng user_classifications trong cơ sở dữ liệu
 * Bảng quản lý phân loại sản phẩm của người dùng
 */
@Entity('user_classifications')
export class UserClassification {
  /**
   * ID của phân loại
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Loại phân loại
   */
  @Column({ name: 'type', type: 'text', nullable: false, comment: 'Loại phân loại' })
  type: string;

  /**
   * Giá của phân loại (dưới dạng JSON)
   */
  @Column({ name: 'price', type: 'jsonb', nullable: true, comment: '<PERSON><PERSON><PERSON> của phân loại (dưới dạng JSON)' })
  price: any;

  /**
   * ID sản phẩm
   */
  @Column({ name: 'product_id', type: 'integer', nullable: true, comment: 'ID sản phẩm' })
  productId: number;

  /**
   * Metadata chứa custom fields và thông tin bổ sung
   */
  @Column({
    name: 'metadata',
    type: 'jsonb',
    nullable: false,
    default: () => "'{\"customFields\": []}'::jsonb", // Phù hợp với database schema
    comment: 'Metadata chứa custom fields và thông tin bổ sung',
  })
  metadata: any;
}
