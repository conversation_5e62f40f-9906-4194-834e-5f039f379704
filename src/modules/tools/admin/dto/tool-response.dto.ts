import { ApiProperty } from '@nestjs/swagger';
import { ToolStatusEnum } from '../../constants/tool-status.enum';
import { AccessTypeEnum } from '../../constants/access-type.enum';
import { EmployeeInfoDto } from './employee-info.dto';

/**
 * DTO cho thông tin phiên bản tool
 */
export class VersionDto {
  @ApiProperty({
    description: 'ID của phiên bản',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên phiên bản',
    example: 'v1.0.0',
  })
  versionName: string;

  @ApiProperty({
    description: 'Số phiên bản (deprecated)',
    example: 1,
    required: false,
  })
  versionNumber?: number;

  @ApiProperty({
    description: 'Tên tool trong định nghĩa code',
    example: 'searchTool',
  })
  toolName: string;

  @ApiProperty({
    description: '<PERSON><PERSON> tả chi tiết về chức năng của tool',
    example: 'Tìm kiếm thông tin từ nhiều nguồn dữ liệu khác nhau',
    nullable: true,
  })
  toolDescription: string | null;

  @ApiProperty({
    description: 'Tham số của tool',
    example: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Câu truy vấn tìm kiếm',
        },
      },
      required: ['query'],
    },
  })
  parameters: Record<string, any>;

  @ApiProperty({
    description: 'Mô tả những thay đổi so với phiên bản trước',
    example: 'Thêm tham số lọc theo ngày',
    nullable: true,
  })
  changeDescription: string | null;

  @ApiProperty({
    description: 'Trạng thái của phiên bản',
    enum: ToolStatusEnum,
    example: ToolStatusEnum.APPROVED,
  })
  status: ToolStatusEnum;

  @ApiProperty({
    description: 'Thời điểm tạo phiên bản (unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thông tin người tạo phiên bản',
    type: EmployeeInfoDto,
  })
  createdBy: EmployeeInfoDto;
}

/**
 * DTO cho thông tin tool trong danh sách
 */
export class ToolListItemDto {
  @ApiProperty({
    description: 'ID của tool',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên của tool',
    example: 'Công cụ tìm kiếm',
  })
  name: string;

  @ApiProperty({
    description: 'Mô tả về tool',
    example: 'Công cụ giúp tìm kiếm thông tin từ nhiều nguồn',
    nullable: true,
  })
  description: string | null;

  @ApiProperty({
    description: 'Loại quyền truy cập',
    enum: AccessTypeEnum,
    example: AccessTypeEnum.PUBLIC,
  })
  accessType: AccessTypeEnum;

  @ApiProperty({
    description: 'Trạng thái của tool',
    enum: ToolStatusEnum,
    example: ToolStatusEnum.APPROVED,
  })
  status: ToolStatusEnum;

  @ApiProperty({
    description: 'Thời điểm tạo tool (unix timestamp)',
    example: 1625097600000,
  })
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật tool (unix timestamp)',
    example: 1625097600000,
  })
  updatedAt: number;

  @ApiProperty({
    description: 'ID của phiên bản mặc định',
    example: '550e8400-e29b-41d4-a716-446655440000',
    nullable: true,
  })
  versionDefault: string | null;

  @ApiProperty({
    description: 'Tên phiên bản hiển thị',
    example: 'v1.0.0',
    nullable: true,
  })
  versionName: string | null;



  @ApiProperty({
    description: 'Thời điểm xóa mềm (unix timestamp)',
    example: 1625097600000,
    nullable: true,
  })
  deletedAt?: number | null;

  @ApiProperty({
    description: 'Thông tin người xóa tool',
    type: 'object',
    properties: {
      name: { type: 'string', example: 'Nguyễn Văn A' },
      email: { type: 'string', example: '<EMAIL>' }
    },
    nullable: true,
  })
  deletedBy?: { name: string; email: string } | null;

  @ApiProperty({
    description: 'Thông tin người tạo tool',
    type: EmployeeInfoDto,
    required: false,
  })
  createdBy?: EmployeeInfoDto;

  @ApiProperty({
    description: 'Thông tin người cập nhật tool',
    type: EmployeeInfoDto,
    required: false,
  })
  updatedBy?: EmployeeInfoDto;
}

/**
 * DTO cho thông tin phiên bản đơn giản
 */
export class SimpleVersionDto {
  @ApiProperty({
    description: 'ID của phiên bản',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Tên phiên bản hiển thị',
    example: 'v1.0.0',
  })
  versionName: string;
}

/**
 * DTO cho thông tin chi tiết tool
 */
export class ToolDetailDto extends ToolListItemDto {
  @ApiProperty({
    description: 'Phiên bản mặc định của tool',
    type: VersionDto,
    nullable: true,
  })
  defaultVersion: VersionDto | null;

  @ApiProperty({
    description: 'Danh sách các phiên bản của tool',
    type: [SimpleVersionDto],
  })
  versions: SimpleVersionDto[];
}
