import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsInt, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@/common/dto/query.dto';

/**
 * DTO cho query parameters khi lấy danh sách tag
 */
export class TagQueryDto extends QueryDto {

  /**
   * Tìm kiếm theo tên
   * @example "VIP"
   */
  @ApiProperty({
    description: 'Tìm kiếm theo tên',
    example: 'VIP',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên phải là chuỗi' })
  name?: string;


}
