import { Column, Entity, PrimaryColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng agents_base trong cơ sở dữ liệu
 * Bảng lưu trạng thái đặc biệt của agent, đánh dấu agent đang hoạt động hoặc thuộc nhóm cơ bản
 */
@Entity('agents_base')
export class AgentBase {
  /**
   * UUID tham chiếu từ agents.id
   */
  @PrimaryColumn('uuid')
  id: string;

  /**
   * ID nhân viên tạo
   */
  @Column({ name: 'created_by', nullable: true })
  createdBy: number;

  /**
   * ID nhân viên cập nhật
   */
  @Column({ name: 'updated_by', nullable: true })
  updatedBy: number;

  /**
   * ID nhân viên xóa
   */
  @Column({ name: 'deleted_by', nullable: true })
  deletedBy: number;

  /**
   * Trạng thái hoạt động (chỉ một agent có active = TRUE tại một thời điểm)
   */
  @Column({ type: 'boolean', default: false })
  active: boolean;

  /**
   * UUID tham chiếu đến bảng base_models
   * ID của model base (nếu có)
   */
  @Column({ name: 'model_base_id', type: 'uuid', nullable: true })
  modelBaseId: string | null;

  /**
   * UUID tham chiếu đến bảng fine_tuning_models
   * ID của fine-tuning model (nếu có)
   */
  @Column({ name: 'model_finetuning_id', type: 'uuid', nullable: true })
  modelFinetuningId: string | null;
}
