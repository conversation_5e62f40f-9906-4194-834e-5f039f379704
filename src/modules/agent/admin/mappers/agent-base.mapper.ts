import { Agent, AgentBase } from '@modules/agent/entities';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { CdnService } from '@shared/services/cdn.service';
import { AgentBaseListItemDto, AgentBaseResponseDto, ModelInfoDto } from '../dto/agent-base';
import { MultiAgentRelationResponseDto } from '../dto/agent-base';
import { AvatarUrlHelper } from '../helpers/avatar-url.helper';
import { EmployeeInfoMapper } from './employee-info.mapper';
import { ModelConfigMapper } from './model-config.mapper';
import { VectorStoreMapper } from './vector-store.mapper';

/**
 * Mapper cho việc chuyển đổi từ entity AgentBase sang DTO
 */
export class AgentBaseMapper {
  /**
   * Chuyển đổi từ entity sang DTO danh sách
   * @param agentBase Entity AgentBase
   * @param agent Entity Agent
   * @param cdnService Service để tạo URL CDN
   * @param modelInfo Thông tin model từ base model hoặc finetuning model
   * @returns AgentBaseListItemDto
   */
  static toListItemDto(
    agentBase: AgentBase,
    agent: Agent,
    cdnService: CdnService,
    modelInfo?: ModelInfoDto
  ): AgentBaseListItemDto {
    const dto = new AgentBaseListItemDto();

    if (agent) {
      dto.id = agent.id;
      dto.name = agent.name;
      dto.avatar = agent.avatar
        ? AvatarUrlHelper.generateViewUrl(cdnService, agent.avatar)
        : null;
      dto.status = agent.status as AgentStatusEnum;
      dto.createdAt = agent.createdAt;
    }

    if (agentBase) {
      dto.active = agentBase.active || false;
    }

    // Thông tin model
    if (modelInfo) {
      dto.model_id = modelInfo.model_id;
      dto.type_provider = modelInfo.typeProvider;
      dto.model = modelInfo.model_id || 'Unknown Model';
    } else {
      dto.model_id = null;
      dto.type_provider = null;
      dto.model = 'Unknown Model';
    }

    return dto;
  }

  /**
   * Chuyển đổi từ entity sang DTO chi tiết
   * @param agentBase Entity AgentBase
   * @param agent Entity Agent
   * @param avatarUrlUpload URL tải lên avatar (nếu có)
   * @param cdnService Service để tạo URL CDN
   * @param employeeInfoService Service để lấy thông tin nhân viên
   * @param multiAgentRelations Danh sách quan hệ multi-agent
   * @param modelInfo Thông tin model từ base model hoặc finetuning model
   * @returns Promise<AgentBaseResponseDto>
   */
  static async toResponseDto(
    agentBase: AgentBase,
    agent: Agent | null | undefined,
    avatarUrlUpload?: string,
    cdnService?: CdnService,
    employeeInfoService?: any,
    multiAgentRelations?: MultiAgentRelationResponseDto[],
    modelInfo?: ModelInfoDto,
  ): Promise<AgentBaseResponseDto> {
    const dto = new AgentBaseResponseDto();

    if (agent) {
      dto.id = agent.id;
      dto.name = agent.name;
      dto.avatar = agent.avatar && cdnService
        ? AvatarUrlHelper.generateViewUrl(cdnService, agent.avatar)
        : null;
      dto.instruction = agent.instruction;
      dto.status = agent.status as AgentStatusEnum;

      // Cấu hình model
      if (agent.modelConfig) {
        dto.modelConfig = ModelConfigMapper.toResponseDto(agent.modelConfig);
      }

      // Vector store
      dto.vector = VectorStoreMapper.toDto(agent.vectorStoreId);
    }

    // Thông tin model
    if (modelInfo) {
      dto.model = modelInfo;
    }

    if (agentBase) {
      dto.active = agentBase.active || false;

      // Thông tin người tạo, cập nhật
      if (employeeInfoService) {
        try {
          if (agentBase.createdBy) {
            const createdByInfo = await employeeInfoService.getEmployeeInfo(agentBase.createdBy);
            dto.created = EmployeeInfoMapper.toDto(
              agentBase.createdBy,
              createdByInfo?.name,
              createdByInfo?.avatar,
              agent?.createdAt
            );
          }

          if (agentBase.updatedBy) {
            const updatedByInfo = await employeeInfoService.getEmployeeInfo(agentBase.updatedBy);
            dto.updated = EmployeeInfoMapper.toDto(
              agentBase.updatedBy,
              updatedByInfo?.name,
              updatedByInfo?.avatar,
              agent?.updatedAt
            );
          }
        } catch (error) {
          console.warn(`Không thể lấy thông tin nhân viên: ${error.message}`);
        }
      }
    }

    if (avatarUrlUpload) {
      dto.avatarUrlUpload = avatarUrlUpload;
    }

    // Thêm thông tin quan hệ multi-agent nếu có
    if (multiAgentRelations && multiAgentRelations.length > 0) {
      dto.multiAgentRelations = multiAgentRelations;
    }

    return dto;
  }
}
