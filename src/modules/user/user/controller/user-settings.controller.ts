import { Body, Controller, Get, Put, Delete, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserSettingsService } from '../service/user-settings.service';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import { ApiResponseDto as AppApiResponse } from '@common/response/api-response-dto';
import { UpdateUserSettingsDto, UserSettingsResponseDto } from '@modules/user/dto/user-settings.dto';

/**
 * Controller xử lý các API liên quan đến user settings
 */
@ApiTags(SWAGGER_API_TAGS.USERS)
@Controller('users/settings')
@UseGuards(JwtUserGuard)
@ApiBearerAuth("JWT-auth")
export class UserSettingsController {
  constructor(
    private readonly userSettingsService: UserSettingsService,
  ) {}

  /**
   * Lấy user settings của người dùng hiện tại
   * @param user Thông tin người dùng từ JWT
   * @returns User settings của người dùng
   */
  @Get()
  @ApiOperation({ summary: 'Lấy user settings của người dùng hiện tại' })
  @ApiResponse({
    status: 200,
    description: 'Lấy user settings thành công',
    schema: AppApiResponse.getSchema(UserSettingsResponseDto)
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async getUserSettings(@CurrentUser() user: JwtPayload) {
    const result = await this.userSettingsService.getUserSettings(user.id);
    return AppApiResponse.success(result);
  }

  /**
   * Cập nhật user settings của người dùng hiện tại
   * @param user Thông tin người dùng từ JWT
   * @param updateUserSettingsDto Dữ liệu cập nhật user settings
   * @returns User settings đã được cập nhật
   */
  @Put()
  @ApiOperation({ summary: 'Cập nhật user settings của người dùng hiện tại' })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật user settings thành công',
    schema: AppApiResponse.getSchema(UserSettingsResponseDto)
  })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async updateUserSettings(
    @CurrentUser() user: JwtPayload,
    @Body() updateUserSettingsDto: UpdateUserSettingsDto,
  ) {
    const result = await this.userSettingsService.updateUserSettings(
      user.id,
      updateUserSettingsDto,
    );
    return AppApiResponse.success(result);
  }

  /**
   * Xóa user settings của người dùng hiện tại (reset về mặc định)
   * @param user Thông tin người dùng từ JWT
   * @returns Thông báo xóa thành công
   */
  @Delete()
  @ApiOperation({ summary: 'Xóa user settings của người dùng hiện tại (reset về mặc định)' })
  @ApiResponse({
    status: 200,
    description: 'Xóa user settings thành công',
  })
  @ApiResponse({ status: 401, description: 'Không có quyền truy cập' })
  async deleteUserSettings(@CurrentUser() user: JwtPayload) {
    await this.userSettingsService.deleteUserSettings(user.id);
    return AppApiResponse.success(null, 'User settings đã được xóa thành công');
  }
}
