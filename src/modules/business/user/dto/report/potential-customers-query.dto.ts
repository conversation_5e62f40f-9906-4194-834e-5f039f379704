import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsN<PERSON><PERSON>, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho query parameters của API khách hàng tiềm năng
 */
export class PotentialCustomersQueryDto {
  @ApiPropertyOptional({
    description: 'Số lượng khách hàng tối đa trả về',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Limit phải là số' })
  @Min(1, { message: 'Limit phải lớn hơn 0' })
  @Max(100, { message: 'Limit không được vượt quá 100' })
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'Gi<PERSON> trị đơn hàng tối thiểu',
    example: 100000,
  })
  @IsOptional()
  @IsNumber({}, { message: '<PERSON><PERSON><PERSON> trị đơn hàng tối thiểu phải là số' })
  @Min(0, { message: '<PERSON><PERSON><PERSON> trị đơn hàng tối thiểu phải lớn hơn hoặc bằng 0' })
  @Type(() => Number)
  minOrderValue?: number;

  @ApiPropertyOptional({
    description: 'Số đơn hàng tối thiểu',
    example: 2,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số đơn hàng tối thiểu phải là số' })
  @Min(1, { message: 'Số đơn hàng tối thiểu phải lớn hơn 0' })
  @Type(() => Number)
  minOrderCount?: number;

  @ApiPropertyOptional({
    description: 'Số ngày từ đơn hàng cuối cùng',
    example: 30,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số ngày từ đơn hàng cuối phải là số' })
  @Min(1, { message: 'Số ngày từ đơn hàng cuối phải lớn hơn 0' })
  @Type(() => Number)
  lastOrderDays?: number;
}
