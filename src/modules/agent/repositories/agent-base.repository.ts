import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Agent, AgentBase } from '@modules/agent/entities';
import { PaginatedResult } from '@/common/response';
import { Transactional } from 'typeorm-transactional';

/**
 * Repository cho AgentBase
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến agent c<PERSON> bản
 */
@Injectable()
export class AgentBaseRepository extends Repository<AgentBase> {
  private readonly logger = new Logger(AgentBaseRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentBase, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentBase
   * @returns SelectQueryBuilder cho AgentBase
   */
  private createBaseQuery(): SelectQueryBuilder<AgentBase> {
    return this.createQueryBuilder('agentBase');
  }

  /**
   * Tạo query builder c<PERSON> bản cho AgentBase với join đến Agent
   * @returns SelectQueryBuilder cho AgentBase với join đến Agent
   */
  private createBaseQueryWithAgent(): SelectQueryBuilder<AgentBase> {
    // Không thể join trực tiếp vì không có mối quan hệ được định nghĩa
    return this.createBaseQuery();
  }

  /**
   * Kiểm tra sự tồn tại của agent cơ bản theo ID
   * @param id ID của agent cơ bản
   * @returns true nếu agent cơ bản tồn tại, false nếu không tồn tại
   */
  async existsById(id: string): Promise<boolean> {
    if (!id) return false;

    const count = await this.createBaseQuery()
      .select('1')
      .where('agentBase.id = :id', { id })
      .andWhere('agentBase.deletedBy IS NULL') // Chỉ kiểm tra agents_base chưa bị xóa
      .limit(1)
      .getCount();

    this.logger.debug(`Kiểm tra tồn tại agent base với ID ${id}: ${count > 0}`);
    return count > 0;
  }

  /**
   * Tìm agent cơ bản theo ID
   * @param id ID của agent cơ bản
   * @returns AgentBase nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<AgentBase | null> {
    return this.createBaseQuery()
      .where('agentBase.id = :id', { id })
      .andWhere('agentBase.deletedBy IS NULL') // Chỉ lấy agents_base chưa bị xóa
      .getOne();
  }

  /**
   * Tìm agent cơ bản đang active
   * @returns AgentBase đang active nếu tìm thấy, null nếu không tìm thấy
   */
  async findActive(): Promise<AgentBase | null> {
    return this.createBaseQuery()
      .where('agentBase.active = :active', { active: true })
      .andWhere('agentBase.deletedBy IS NULL') // Chỉ lấy agents_base chưa bị xóa
      .getOne();
  }

/**
 * Đảo ngược trạng thái active cho agent cơ bản
 * @param id ID của agent cơ bản cần đảo ngược trạng thái
 * @returns Số lượng bản ghi đã được cập nhật
 */
async setActive(id: string): Promise<number> {
  // Lấy trạng thái active hiện tại của agent
  const agent = await this.createQueryBuilder()
    .select('active')
    .where('id = :id', { id })
    .getOne();

  if (!agent) {
    return 0; // Trả về 0 nếu không tìm thấy agent
  }

  // Cập nhật trạng thái active mới
  const result = await this.createQueryBuilder()
    .update(AgentBase)
    .set({ active: !agent.active })
    .where('id = :id', { id })
    .execute();

  return result.affected || 0;
}

  /**
   * Deactivate tất cả agent base (set active = false)
   * @returns Số lượng bản ghi đã được cập nhật
   */
  async deactivateAll(): Promise<number> {
    const result = await this.createQueryBuilder()
      .update(AgentBase)
      .set({ active: false })
      .where('active = :active', { active: true })
      .execute();

    this.logger.debug(`Deactivated ${result.affected || 0} agent base(s)`);
    return result.affected || 0;
  }

  /**
   * Tìm danh sách agent base với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên agent)
   * @param active Lọc theo trạng thái active
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent base với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    active?: boolean,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AgentBase> & { agents?: Agent[] }> {
    const qb = this.createBaseQuery()
      .where('agentBase.deletedBy IS NULL'); // Chỉ lấy agents_base chưa bị xóa

    // Thêm điều kiện lọc theo trạng thái active nếu có
    if (active !== undefined) {
      qb.andWhere('agentBase.active = :active', { active });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit).take(limit);

    // Lấy danh sách agent base
    const [items, total] = await qb.getManyAndCount();

    // Lấy danh sách ID của các agent base
    const agentIds = items.map(item => item.id);

    // Lấy thông tin các agent tương ứng
    let agents: Agent[] = [];
    if (agentIds.length > 0) {
      // Tạo query builder cho Agent - chỉ select các fields cần thiết
      const agentQb = this.dataSource.createQueryBuilder()
        .select([
          'agent.id',
          'agent.name',
          'agent.avatar',
          'agent.modelConfig',
          'agent.instruction',
          'agent.vectorStoreId',
          'agent.createdAt',
          'agent.updatedAt',
          'agent.deletedAt',
          'agent.isForSale',
          'agent.status'
        ])
        .from(Agent, 'agent')
        .where('agent.id IN (:...ids)', { ids: agentIds });

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        agentQb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
      }

      // Thêm điều kiện chỉ lấy các agent không bị xóa mềm
      agentQb.andWhere('agent.deletedAt IS NULL');

      // Xử lý sắp xếp
      agentQb.orderBy(`agent.${sortBy}`, sortDirection);

      agents = await agentQb.getMany();
    }

    return {
      items,
      agents,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Xóa mềm agent base (cập nhật trường deletedBy)
   * @param id ID của agent base cần xóa mềm
   * @param employeeId ID của nhân viên thực hiện xóa
   * @returns true nếu xóa thành công, false nếu không
   */
  @Transactional()
  async customSoftDelete(id: string, employeeId: number): Promise<boolean> {
    try {
      this.logger.debug(`Thực hiện xóa mềm agent base với ID: ${id}`);

      // Cập nhật trường deletedBy
      const result = await this.createQueryBuilder()
        .update(AgentBase)
        .set({
          deletedBy: employeeId
        })
        .where('id = :id', { id })
        .execute();

      const success = result.affected !== null && result.affected !== undefined && result.affected > 0;
      this.logger.debug(`Kết quả xóa mềm agent base: ${success ? 'Thành công' : 'Thất bại'}`);

      return success;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa mềm agent base: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm danh sách agent base đã xóa với phân trang (tối ưu với JOIN)
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tìm theo tên agent)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent base đã xóa với phân trang và thông tin employee
   */
  async findDeletedPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'deletedBy',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AgentBase> & { agents?: Agent[]; deletedEmployees?: any[] }> {
    // Query chính để lấy agent base đã xóa
    const qb = this.createBaseQuery()
      .where('agentBase.deletedBy IS NOT NULL');

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit).take(limit);

    // Lấy danh sách agent base đã xóa
    const [items, total] = await qb.getManyAndCount();

    // Lấy thông tin employees đã xóa (batch query)
    const deletedEmployeeIds = [...new Set(items.map(item => item.deletedBy).filter(Boolean))];
    let deletedEmployees: any[] = [];

    if (deletedEmployeeIds.length > 0) {
      const employeeQb = this.dataSource.createQueryBuilder()
        .select([
          'employee.id',
          'employee.fullName',
          'employee.avatar'
        ])
        .from('employees', 'employee')
        .where('employee.id IN (:...ids)', { ids: deletedEmployeeIds });

      const employeeResults = await employeeQb.getRawMany();

      // Map employee info với agent base
      deletedEmployees = items.map(agentBase => {
        const employee = employeeResults.find(emp => emp.employee_id === agentBase.deletedBy);
        return {
          agentBaseId: agentBase.id,
          employeeId: agentBase.deletedBy,
          employeeName: employee?.employee_fullName || 'Unknown',
          employeeAvatar: employee?.employee_avatar || null,
        };
      });
    }

    // Lấy danh sách ID của các agent base
    const agentIds = items.map(item => item.id);

    // Lấy thông tin các agent tương ứng
    let agents: Agent[] = [];
    if (agentIds.length > 0) {
      // Tạo query builder cho Agent - chỉ select các fields cần thiết
      const agentQb = this.dataSource.createQueryBuilder()
        .select([
          'agent.id',
          'agent.name',
          'agent.avatar',
          'agent.modelConfig',
          'agent.instruction',
          'agent.vectorStoreId',
          'agent.createdAt',
          'agent.updatedAt',
          'agent.deletedAt',
          'agent.isForSale',
          'agent.status'
        ])
        .from(Agent, 'agent')
        .where('agent.id IN (:...ids)', { ids: agentIds });

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        agentQb.andWhere('agent.name ILIKE :search', { search: `%${search}%` });
      }

      // Xử lý sắp xếp
      agentQb.orderBy(`agent.${sortBy === 'deletedBy' ? 'createdAt' : sortBy}`, sortDirection);

      agents = await agentQb.getMany();
    }

    return {
      items,
      agents,
      deletedEmployees,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Khôi phục agent base đã xóa
   * @param id ID của agent base cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục
   * @returns true nếu khôi phục thành công, false nếu không
   */
  @Transactional()
  async restoreAgentBase(id: string, employeeId: number): Promise<boolean> {
    try {
      this.logger.debug(`Thực hiện khôi phục agent base với ID: ${id}`);

      // Cập nhật trường deletedBy về null và updatedBy
      const result = await this.createQueryBuilder()
        .update(AgentBase)
        .set({
          deletedBy: () => 'NULL',
          updatedBy: employeeId
        })
        .where('id = :id', { id })
        .andWhere('deletedBy IS NOT NULL')
        .execute();

      const success = result.affected !== null && result.affected !== undefined && result.affected > 0;
      this.logger.debug(`Kết quả khôi phục agent base: ${success ? 'Thành công' : 'Thất bại'}`);

      return success;
    } catch (error) {
      this.logger.error(`Lỗi khi khôi phục agent base: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Tìm agent base đã xóa theo ID
   * @param id ID của agent base
   * @returns AgentBase nếu tìm thấy và đã bị xóa, null nếu không tìm thấy
   */
  async findDeletedById(id: string): Promise<AgentBase | null> {
    return this.createBaseQuery()
      .where('agentBase.id = :id', { id })
      .andWhere('agentBase.deletedBy IS NOT NULL')
      .getOne();
  }
}
