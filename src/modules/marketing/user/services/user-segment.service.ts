import { Injectable } from '@nestjs/common';
import { UserSegmentRepository } from '../repositories/user-segment.repository';
import { UserAudienceRepository } from '../repositories/user-audience.repository';
import {
  CreateSegmentDto,
  UpdateSegmentDto,
  SegmentResponseDto,
  SegmentStatsDto,
  SegmentQueryDto,
  SegmentAudienceResponseDto,
  SegmentAudienceQueryDto,
  SegmentPreviewDto,
  SegmentPreviewResponseDto,
  ConditionType,
  OperatorType,
} from '../dto/segment';
import {
  UserSegment,
  UserAudience,
  UserAudienceCustomField,
} from '../entities';
import { Transactional } from 'typeorm-transactional';
import { AppException, ErrorCode } from '@common/exceptions';
import { PaginatedResult } from '@/common/response';

/**
 * Service xử lý logic liên quan đến segment
 */
@Injectable()
export class UserSegmentService {
  constructor(
    private readonly userSegmentRepository: UserSegmentRepository,
    private readonly userAudienceRepository: UserAudienceRepository,
  ) {}

  /**
   * Tạo segment mới
   * @param userId ID của người dùng
   * @param createSegmentDto Dữ liệu tạo segment
   * @returns Thông tin segment đã tạo
   */
  async create(
    userId: number,
    createSegmentDto: CreateSegmentDto,
  ): Promise<SegmentResponseDto> {
    const now = Math.floor(Date.now() / 1000);

    const segment = new UserSegment();
    segment.userId = userId;
    segment.name = createSegmentDto.name;
    segment.description = createSegmentDto.description || '';
    segment.criteria = createSegmentDto.criteria;
    segment.createdAt = now;
    segment.updatedAt = now;

    const savedSegment = await this.userSegmentRepository.save(segment);
    return this.mapToDto(savedSegment as UserSegment);
  }

  /**
   * Cập nhật segment
   * @param userId ID của người dùng
   * @param id ID của segment
   * @param updateSegmentDto Dữ liệu cập nhật segment
   * @returns Thông tin segment đã cập nhật
   */
  async update(
    userId: number,
    id: number,
    updateSegmentDto: UpdateSegmentDto,
  ): Promise<SegmentResponseDto> {
    const segment = await this.userSegmentRepository.findOne({
      where: { id, userId },
    });
    if (!segment) {
      throw new AppException(ErrorCode.SEGMENT_NOT_FOUND, `Segment với ID ${id} không tồn tại`);
    }

    if (updateSegmentDto.name !== undefined) {
      segment.name = updateSegmentDto.name;
    }

    if (updateSegmentDto.description !== undefined) {
      segment.description = updateSegmentDto.description;
    }

    if (updateSegmentDto.criteria !== undefined) {
      segment.criteria = updateSegmentDto.criteria;
    }

    segment.updatedAt = Math.floor(Date.now() / 1000);

    const updatedSegment = await this.userSegmentRepository.save(segment);
    return this.mapToDto(updatedSegment as UserSegment);
  }

  /**
   * Xóa segment
   * @param userId ID của người dùng
   * @param id ID của segment
   * @returns Thông tin xóa thành công
   */
  @Transactional()
  async remove(userId: number, id: number): Promise<{ success: boolean }> {
    const segment = await this.userSegmentRepository.findOne({
      where: { id, userId },
    });
    if (!segment) {
      throw new AppException(ErrorCode.SEGMENT_NOT_FOUND, `Segment với ID ${id} không tồn tại`);
    }

    try {
      // Cập nhật các campaign có tham chiếu đến segment này
      // Đặt segmentId = null cho các campaign đang sử dụng segment này
      await this.userSegmentRepository.executeQuery(
        `UPDATE user_campaigns SET segment_id = NULL WHERE segment_id = $1 AND user_id = $2`,
        [id, userId],
      );

      // Sau khi đã xử lý các khóa ngoại, tiến hành xóa segment
      await this.userSegmentRepository.remove(segment);
      return { success: true };
    } catch (error) {
      console.error('Lỗi khi xóa segment:', error);
      throw new AppException(
        ErrorCode.DATABASE_ERROR,
        `Không thể xóa segment: ${error.message}`,
        error
      );
    }
  }

  /**
   * Lấy danh sách segment của người dùng với phân trang và tìm kiếm
   * @param userId ID của người dùng
   * @param queryDto Query parameters cho phân trang và tìm kiếm
   * @returns Danh sách segment với phân trang
   */
  async findAll(userId: number, queryDto: SegmentQueryDto): Promise<PaginatedResult<SegmentResponseDto>> {
    const { page, limit, search, sortBy, sortDirection } = queryDto;

    // Tính toán offset
    const offset = (page - 1) * limit;

    // Tạo query builder
    const queryBuilder = this.userSegmentRepository.createQueryBuilder('segment')
      .where('segment.userId = :userId', { userId });

    // Thêm điều kiện tìm kiếm
    if (search) {
      queryBuilder.andWhere(
        '(segment.name ILIKE :search OR segment.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Thêm sắp xếp
    queryBuilder.orderBy(`segment.${sortBy}`, sortDirection);

    // Thêm phân trang
    queryBuilder.skip(offset).take(limit);

    // Lấy dữ liệu và tổng số
    const [segments, total] = await queryBuilder.getManyAndCount();

    // Chuyển đổi sang DTO
    const items = segments.map((segment) => this.mapToDto(segment));

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Lấy thông tin segment theo ID
   * @param userId ID của người dùng
   * @param id ID của segment
   * @returns Thông tin segment
   */
  async findOne(userId: number, id: number): Promise<SegmentResponseDto> {
    const segment = await this.userSegmentRepository.findOne({
      where: { id, userId },
    });
    if (!segment) {
      throw new AppException(ErrorCode.SEGMENT_NOT_FOUND, `Segment với ID ${id} không tồn tại`);
    }

    return this.mapToDto(segment);
  }

  /**
   * Lấy thống kê của segment
   * @param userId ID của người dùng
   * @param id ID của segment
   * @returns Thống kê segment
   */
  async getStats(userId: number, id: number): Promise<SegmentStatsDto> {
    const segment = await this.userSegmentRepository.findOne({
      where: { id, userId },
    });
    if (!segment) {
      throw new AppException(ErrorCode.SEGMENT_NOT_FOUND, `Segment với ID ${id} không tồn tại`);
    }

    // Lấy tổng số audience của người dùng
    const totalAudienceCount = await this.userAudienceRepository.count({
      where: { userId },
    });

    // Lấy danh sách audience phù hợp với điều kiện của segment
    const matchedAudiences = await this.getAudiencesInSegment(userId, segment);
    const matchedCount = matchedAudiences.length;

    const stats = new SegmentStatsDto();
    stats.segmentId = segment.id;
    stats.segmentName = segment.name;
    stats.totalAudiences = matchedCount;
    stats.percentageOfTotal =
      totalAudienceCount > 0 ? matchedCount / totalAudienceCount : 0;
    stats.updatedAt = Math.floor(Date.now() / 1000);

    return stats;
  }

  /**
   * Lấy danh sách audience trong segment
   * @param userId ID của người dùng
   * @param segment Segment cần lấy audience
   * @returns Danh sách audience
   */
  async getAudiencesInSegment(
    userId: number,
    segment: UserSegment,
  ): Promise<UserAudience[]> {
    // Lấy tất cả audience của người dùng
    const allAudiences = await this.userAudienceRepository.find({
      where: { userId },
    });

    // Lấy tất cả các trường tùy chỉnh của các audience
    const audienceIds = allAudiences.map((a) => a.id);
    const customFields = await this.getCustomFieldsForAudiences(audienceIds);

    // Lọc audience theo điều kiện của segment
    return allAudiences.filter((audience) => {
      const audienceCustomFields = customFields.filter(
        (cf) => cf.audienceId === audience.id,
      );
      return this.evaluateCriteria(
        segment.criteria,
        audience,
        audienceCustomFields,
      );
    });
  }

  /**
   * Lấy danh sách audience trong segment với phân trang
   * @param userId ID của người dùng
   * @param segmentId ID của segment
   * @param queryDto Query parameters cho phân trang và tìm kiếm
   * @returns Danh sách audience với phân trang
   */
  async getSegmentAudiences(
    userId: number,
    segmentId: number,
    queryDto: SegmentAudienceQueryDto,
  ): Promise<PaginatedResult<SegmentAudienceResponseDto>> {
    // Kiểm tra segment tồn tại và thuộc về user
    const segment = await this.userSegmentRepository.findOne({
      where: { id: segmentId, userId },
    });

    if (!segment) {
      throw new AppException(
        ErrorCode.SEGMENT_NOT_FOUND,
        `Segment với ID ${segmentId} không tồn tại`,
      );
    }

    // Lấy tất cả audience phù hợp với segment
    const allMatchedAudiences = await this.getAudiencesInSegment(userId, segment);

    // Lọc theo email nếu có
    let filteredAudiences = allMatchedAudiences;
    if (queryDto.email) {
      filteredAudiences = allMatchedAudiences.filter((audience) =>
        audience.email.toLowerCase().includes(queryDto.email!.toLowerCase()),
      );
    }

    // Tính toán phân trang
    const { page, limit } = queryDto;
    const total = filteredAudiences.length;
    const offset = (page - 1) * limit;
    const paginatedAudiences = filteredAudiences.slice(offset, offset + limit);

    // Chuyển đổi thành DTO
    const data: SegmentAudienceResponseDto[] = paginatedAudiences.map((audience) => ({
      id: audience.id,
      email: audience.email,
    }));

    // Tạo thông tin phân trang
    const totalPages = Math.ceil(total / limit);

    return {
      items: data,
      meta: {
        totalItems: total,
        itemCount: data.length,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
        hasItems: data.length > 0,
      },
    };
  }

  /**
   * Preview segment - tính tổng số audience phù hợp với điều kiện mà không tạo segment
   * @param userId ID của người dùng
   * @param previewDto Dữ liệu segment để preview
   * @returns Thống kê số audience phù hợp
   */
  async previewSegmentAudienceCount(
    userId: number,
    previewDto: SegmentPreviewDto,
  ): Promise<SegmentPreviewResponseDto> {
    // Lấy tổng số audience của người dùng
    const totalAudienceCount = await this.userAudienceRepository.count({
      where: { userId },
    });

    // Tạo một segment tạm thời để sử dụng logic hiện tại
    const tempSegment = new UserSegment();
    tempSegment.criteria = previewDto.criteria;

    // Lấy danh sách audience phù hợp với điều kiện
    const matchedAudiences = await this.getAudiencesInSegment(userId, tempSegment);
    const matchedCount = matchedAudiences.length;

    // Tính tỷ lệ phần trăm
    const percentageOfTotal = totalAudienceCount > 0 ? matchedCount / totalAudienceCount : 0;

    // Tạo response
    const response = new SegmentPreviewResponseDto();
    response.totalAudiences = matchedCount;
    response.percentageOfTotal = percentageOfTotal;
    response.totalUserAudiences = totalAudienceCount;

    return response;
  }

  /**
   * Lấy các trường tùy chỉnh cho danh sách audience
   * @param audienceIds Danh sách ID của audience
   * @returns Danh sách trường tùy chỉnh
   */
  private async getCustomFieldsForAudiences(
    audienceIds: number[],
  ): Promise<UserAudienceCustomField[]> {
    // TODO: Implement this method to get custom fields for audiences
    // This is a placeholder implementation
    return [];
  }

  /**
   * Đánh giá điều kiện của segment
   * @param criteria Điều kiện của segment
   * @param audience Audience cần đánh giá
   * @param customFields Các trường tùy chỉnh của audience
   * @returns true nếu audience phù hợp với điều kiện
   */
  private evaluateCriteria(
    criteria: any,
    audience: UserAudience,
    customFields: UserAudienceCustomField[],
  ): boolean {
    // Nếu không có điều kiện, trả về true
    if (!criteria) return true;

    const { conditionType, conditions, groups } = criteria;

    // Đánh giá các điều kiện
    const conditionResults =
      conditions?.map((condition: any) => {
        return this.evaluateCondition(condition, audience, customFields);
      }) || [];

    // Đánh giá các nhóm điều kiện con
    const groupResults =
      groups?.map((group: any) => {
        return this.evaluateCriteria(group, audience, customFields);
      }) || [];

    // Kết hợp kết quả
    const allResults = [...conditionResults, ...groupResults];

    // Nếu không có kết quả nào, trả về true
    if (allResults.length === 0) return true;

    // Đánh giá theo loại điều kiện (AND/OR)
    if (conditionType === ConditionType.AND) {
      return allResults.every((result) => result === true);
    } else {
      return allResults.some((result) => result === true);
    }
  }

  /**
   * Đánh giá một điều kiện
   * @param condition Điều kiện cần đánh giá
   * @param audience Audience cần đánh giá
   * @param customFields Các trường tùy chỉnh của audience
   * @returns true nếu audience phù hợp với điều kiện
   */
  private evaluateCondition(
    condition: any,
    audience: UserAudience,
    customFields: UserAudienceCustomField[],
  ): boolean {
    const { field, operator, value } = condition;

    // Lấy giá trị của trường
    let fieldValue: any;

    // Kiểm tra các trường cơ bản của audience
    if (field === 'email') {
      fieldValue = audience.email;
    } else if (field === 'phone') {
      fieldValue = audience.phone;
    } else {
      // Kiểm tra các trường tùy chỉnh
      const customField = customFields.find((cf) => cf.fieldName === field);
      fieldValue = customField?.fieldValue;
    }

    // Đánh giá theo toán tử
    switch (operator) {
      case OperatorType.EQUALS:
        return fieldValue === value;
      case OperatorType.NOT_EQUALS:
        return fieldValue !== value;
      case OperatorType.CONTAINS:
        return typeof fieldValue === 'string' && fieldValue.includes(value);
      case OperatorType.NOT_CONTAINS:
        return typeof fieldValue === 'string' && !fieldValue.includes(value);
      case OperatorType.GREATER_THAN:
        return fieldValue > value;
      case OperatorType.LESS_THAN:
        return fieldValue < value;
      case OperatorType.IN:
        return Array.isArray(value) && value.includes(fieldValue);
      case OperatorType.NOT_IN:
        return Array.isArray(value) && !value.includes(fieldValue);
      case OperatorType.EXISTS:
        return fieldValue !== undefined && fieldValue !== null;
      case OperatorType.NOT_EXISTS:
        return fieldValue === undefined || fieldValue === null;
      default:
        return false;
    }
  }

  /**
   * Chuyển đổi entity thành DTO
   * @param segment Entity segment
   * @returns DTO segment
   */
  private mapToDto(segment: UserSegment): SegmentResponseDto {
    const dto = new SegmentResponseDto();
    dto.id = segment.id;
    dto.name = segment.name;
    dto.description = segment.description;
    dto.criteria = segment.criteria;
    dto.createdAt = segment.createdAt;
    dto.updatedAt = segment.updatedAt;
    return dto;
  }
}
