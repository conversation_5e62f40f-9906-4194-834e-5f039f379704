import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import {
  WarehouseRepository,
  CustomFieldRepository
} from '@modules/business/repositories';
import { QueryWarehouseDto } from '../dto/warehouse/query-warehouse.dto';
import { WarehouseResponseDto } from '@modules/business/admin/dto/warehouse';
import {
  WarehouseDetailResponseDto,
  PhysicalWarehouseDetailsDto,
  VirtualWarehouseDetailsDto
} from '@modules/business/admin/dto/warehouse';
import { PaginatedResult } from '@common/response';
import { WarehouseValidationHelper } from '@modules/business/admin/helpers';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * Service xử lý logic nghiệp vụ cho kho
 */
@Injectable()
export class AdminWarehouseService {
  private readonly logger = new Logger(AdminWarehouseService.name);

  constructor(
    private readonly warehouseRepository: WarehouseRepository,
    private readonly customFieldRepository: CustomFieldRepository,
    private readonly warehouseValidationHelper: WarehouseValidationHelper
  ) {}

  /**
   * Lấy danh sách kho với phân trang
   * @param queryDto DTO truy vấn
   * @returns Danh sách kho với phân trang
   */
  async findAll(queryDto: QueryWarehouseDto): Promise<PaginatedResult<WarehouseResponseDto>> {
    this.logger.log(`Lấy danh sách kho với phân trang: ${JSON.stringify(queryDto)}`);

    try {
      // Lấy danh sách kho từ repository
      const [warehouses, total] = await this.warehouseRepository.findAllWithPagination(queryDto);

      // Chuyển đổi dữ liệu thành DTO sử dụng constructor
      const items = warehouses.map(warehouse => new WarehouseResponseDto(warehouse));

      // Trả về kết quả với phân trang
      return {
        items,
        meta: {
          totalItems: total,
          itemCount: items.length,
          itemsPerPage: queryDto.limit,
          totalPages: Math.ceil(total / queryDto.limit),
          currentPage: queryDto.page
        }
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kho: ${error.message}`, error.stack);
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_FIND_FAILED,
        'Lỗi khi lấy danh sách kho'
      );
    }
  }

  /**
   * Lấy thông tin chi tiết kho theo ID
   * @param warehouseId ID của kho
   * @returns Thông tin chi tiết kho
   */
  async findOne(warehouseId: number): Promise<WarehouseDetailResponseDto> {
    this.logger.log(`Lấy thông tin chi tiết kho với ID: ${warehouseId}`);

    // Lấy thông tin kho
    const warehouse = await this.warehouseRepository.findByWarehouseId_admin(warehouseId);

    // Kiểm tra kho có tồn tại không
    this.warehouseValidationHelper.validateWarehouseExists(warehouse);

    // Sau khi validate, warehouse chắc chắn không phải là null
    const validWarehouse = warehouse!;
    this.logger.log(`Thông tin kho: ${JSON.stringify(validWarehouse)}`);

    try {
      // Lấy danh sách trường tùy chỉnh của kho
      // const customFields = await this.warehouseCustomFieldRepository.findByWarehouseId_admin(warehouseId); // WarehouseCustomField đã bị xóa
      const customFields = []; // Thay thế bằng mảng rỗng
      this.logger.log(`Số lượng trường tùy chỉnh: ${customFields ? customFields.length : 0}`);

      // Tạo response sử dụng constructor
      const response = new WarehouseDetailResponseDto({
        ...validWarehouse,
        customFields: customFields || []
      });

      // Xử lý chi tiết kho dựa vào loại kho
      if (validWarehouse.type === WarehouseTypeEnum.PHYSICAL) {
        // Lấy thông tin kho vật lý từ repository
        const physicalWarehouse = await this.warehouseRepository.findPhysicalWarehouseByWarehouseId(warehouseId);

        if (physicalWarehouse) {
          this.logger.log(`Đã tìm thấy thông tin kho vật lý: ${JSON.stringify(physicalWarehouse)}`);

          // Khởi tạo đối tượng PhysicalWarehouseDetailsDto
          response.details = new PhysicalWarehouseDetailsDto({
            address: physicalWarehouse.address,
            capacity: physicalWarehouse.capacity
          });
        } else {
          this.logger.warn(`Không tìm thấy thông tin chi tiết kho vật lý với ID: ${warehouseId}`);
          response.details = new PhysicalWarehouseDetailsDto({
            address: 'Không có thông tin'
          });
        }
      } else if (validWarehouse.type === WarehouseTypeEnum.VIRTUAL) {
        // Lấy thông tin kho ảo từ repository
        const virtualWarehouse = await this.warehouseRepository.findVirtualWarehouseByWarehouseId(warehouseId);

        if (virtualWarehouse) {
          this.logger.log(`Đã tìm thấy thông tin kho ảo: ${JSON.stringify(virtualWarehouse)}`);

          // Khởi tạo đối tượng VirtualWarehouseDetailsDto
          response.details = new VirtualWarehouseDetailsDto({
            associatedSystem: virtualWarehouse.associatedSystem,
            purpose: virtualWarehouse.purpose
          });
        } else {
          this.logger.warn(`Không tìm thấy thông tin chi tiết kho ảo với ID: ${warehouseId}`);
          response.details = new VirtualWarehouseDetailsDto({});
        }
      } else {
        // Khởi tạo details trống cho các loại kho khác
        response.details = new PhysicalWarehouseDetailsDto({
          address: 'Không có thông tin'
        });
      }

      this.logger.log(`Response cuối cùng: ${JSON.stringify(response)}`);
      return response;

    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin chi tiết kho: ${error.message}`, error.stack);
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_FIND_FAILED,
        'Lỗi khi lấy thông tin chi tiết kho'
      );
    }
  }
}
