import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@/common/dto/api-error-response.dto';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import { BaseModel } from '@modules/model-training/entities';
import {
  ClassSerializerInterceptor,
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  UseInterceptors
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags
} from '@nestjs/swagger';
import { BaseModelQueryDto } from '../../dto/base-model/base-model-query.dto';
import { BaseModelUserResponseDto } from '../../dto/base-model/base-model-user-response.dto';
import { MODEL_TRAINING_ERROR_CODES } from '../../exceptions/model-training.exception';
import { BaseModelUserService } from '../services';

/**
 * Controller xử lý các API liên quan đến quản lý model base cho người dùng
 * Cung cấp các endpoint để:
 * 1. Lấy danh sách model base từ hệ thống có phân trang
 * 2. Lấy danh sách model từ provider của user không phân trang
 */
@ApiTags(SWAGGER_API_TAGS.USER_BASE_MODEL)
@ApiExtraModels(ApiResponseDto, BaseModel, PaginatedResult, ApiErrorResponseDto, BaseModelUserResponseDto)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtUserGuard)
@UseInterceptors(ClassSerializerInterceptor)
@Controller('user/base-models')
export class BaseModelUserController {
  constructor(private readonly baseModelUserService: BaseModelUserService) { }

  /**
   * Lấy danh sách model base từ hệ thống có phân trang
   * Chỉ trả về các model có trạng thái APPROVED
   * @param query Tham số truy vấn và phân trang
   * @returns Danh sách model base có phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách model base từ hệ thống',
    description: 'Trả về danh sách model base có trạng thái APPROVED từ hệ thống với phân trang.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách model base thành công',
    schema: ApiResponseDto.getPaginatedSchema(BaseModelUserResponseDto),
  })
  @ApiErrorResponse(
    MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR
  )
  async getAllBaseModels(
    @Query() query: BaseModelQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<BaseModelUserResponseDto>>> { // Trả về danh sách model base từ hệ thống có phân trang
    const result = await this.baseModelUserService.findAllByUser(query);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy tất cả model từ provider của user không phân trang
   * @param providerId ID của provider
   * @param userId ID của user (lấy từ token)
   * @returns Danh sách tất cả model từ provider
   */
  @Get('provider/:providerId')
  @ApiOperation({
    summary: 'Lấy tất cả model từ provider của user',
    description: 'Trả về danh sách tất cả model từ provider đã tích hợp của user, không phân trang.',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách model từ provider thành công',
    schema: ApiResponseDto.getArraySchema(BaseModelUserResponseDto),
  })
  @ApiErrorResponse(
    MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
    MODEL_TRAINING_ERROR_CODES.USER_PROVIDER_NOT_FOUND
  )
  async getModelsFromProvider(
    @Param('providerId') providerId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<BaseModelUserResponseDto[]>> { // Trả về danh sách tất cả model từ provider của user
    const result = await this.baseModelUserService.findAllByUserProvider(providerId, userId);
    return ApiResponseDto.success(result);
  }


}